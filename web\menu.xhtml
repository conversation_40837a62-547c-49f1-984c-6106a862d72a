<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:o="http://omnifaces.org/ui"
      xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="assets/img/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <link type="text/css" href="assets/css/menu.css" rel="stylesheet" />
            <link type="text/css" href="assets/css/bootstrap.css" rel="stylesheet" />
            <link type="text/css" href="assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="assets/css/animate.css" rel="stylesheet" />
            <link type="text/css" href="assets/css/stylePage.css" rel="stylesheet" />
            <script src="assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
            <script
                src="https://maps.googleapis.com/maps/api/js?key=#{login.googleApiOper}">
            </script>
            <link type="text/css" href="assets/css/font-awesome.min.css" rel="stylesheet" />
            <style>
                [id*="chkCorporativo"]{
                    top: 26px !important;
                    position: absolute;
                    right: 10px;
                    font-weight: bold;
                    color: #3C8DBC !important;
                    border: thin solid #3C8DBC !important;
                    border-radius: 30px;
                    padding: 9px 0px 4px 17px !important;
                    background-color: #FFF !important;
                    text-align: center !important;
                    box-shadow: 2px 2px 3px #ccc;
                }

                .ui-datatable-scrollable-body{
                    height: 200px !important;
                }


                @media only screen and (max-width: 3000px) and (min-width: 701px) {
                    [id*="tabelaCxForte"] thead tr th:nth-child(1),
                    [id*="tabelaCxForte"] tbody tr td:nth-child(1){
                        min-width: 130px !important;
                        width:130px !important;
                        max-width:130px !important;
                        text-align: center !important
                    }
                }

                @media only screen and (max-width: 701px) and (min-width: 10px) {
                    .divBt{
                        padding-top: 10px !important
                    }
                    .ui-column-title{
                        width: 50% !important;
                        text-align: right !important;
                    }

                    [id*="tabelaCxForte"] span:last-child{
                        width: 40% !important;
                        text-align: left !important;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }

                    [class*="ui-datatable-scrollable-body"],
                    .ui-datatable-scrollable-body,
                    [id*="formCaixaForteCustodia:tabelaCxForte"] > div:nth-child(2){
                        min-height: 250px !important;
                        height: 250px !important;
                        max-height: 250px !important;
                        overflow-x: none !important;
                        overflow-y: auto !important;
                    }

                    .NegDestaque{
                        font-weight: bold !important;
                    }

                    [id*="pnlFundoCustodia"]{

                    }
                }

                [id*="formCaixaForteCustodia"]:eq(0){
                    height: 255px !important;
                    overflow: auto !Important;
                }

                [id*="tabelaCxForte"]  tr td{
                    white-space: nowrap !important;
                }

                [id*="formGuiasAssina"] .ui-dialog.ui-widget-content .ui-dialog-titlebar, body .ui-dialog .ui-dialog-titlebar{
                    background-color:#FFF !important;
                    border-bottom-color: #CCC !important;
                }
            </style>
        </h:head>
        <h:body id="h">
            <f:metadata>
                <f:viewAction action="#{importarMB.Persistencia(login.pp, login.satellite)}"/>
                <f:viewAction action="#{custodia.Persistencia(login.pp)}"/>
                <f:viewAction action="#{valores.Persistencia(login.pp, login.satellite)}"/>
            </f:metadata>

            <div class="h-wrapper" id="divLogo" style="background: url('assets/img/st_back#{login.origem ne '_confederal'? login.origem: login.filial.codfilAc ne '3001'?'_confederal':'_confere'}.png') no-repeat top center; background-size: 62%">

            </div>

            <div class="sate-icon-wrapper" style="height:95% !important; overflow:hidden !important;">
                <div class="sate-icon-grid" style="height:100% !important; overflow:hidden !important; display:none">
                    <p:growl id="msgs"/>
                    <p:growl id="msgDeatalhada" showDetail="true" />
                    <h:form>

                        <div class="ui-g">
                            <div class="ui-g-3" style="padding: 0px; margin-bottom: 30px;">
                                <div class="sate-element">
                                </div>
                            </div>
                            <div class="ui-g-3" style="padding: 0px; margin-bottom: 30px;" ref="operacoes">
                                <div class="sate-element" style="margin-right: 30px;">
                                    <p:commandLink oncomplete="PF('dlgOperacoes').show();">
                                        <p:graphicImage url="assets/img/icone_operacoes.png"/>
                                    </p:commandLink>
                                </div>
                                <div class="sate-element" style="margin-right: 30px;">
                                    <p:commandLink oncomplete="PF('dlgOperacoes').show();">
                                        <p:outputLabel value="#{localemsgs.Operacoes}"
                                                       style="color: black;"/>
                                    </p:commandLink>
                                </div>
                            </div>
                            <div class="ui-g-2" style="padding: 0px; margin-bottom: 30px;" ref="faturamento">
                                <div class="sate-element" style="margin-left: 30px;text-align:center !important;">
                                    <p:commandLink oncomplete="PF('dlgFaturamento').show();">
                                        <p:graphicImage url="assets/img/icone_faturamento_g.png"/>
                                    </p:commandLink>
                                </div>
                                <div class="sate-element" style="margin-left: 30px; text-align:center !important;">
                                    <p:commandLink oncomplete="PF('dlgFaturamento').show();">
                                        <p:outputLabel value="#{localemsgs.Faturamento}"
                                                       style="color: black;"/>
                                    </p:commandLink>
                                </div>
                            </div>
                            <div class="ui-g-2" style="padding: 0px; margin-bottom: 30px;" ref="refeicoes">
                                <div class="sate-element" style="margin-left: 0px;text-align:center !important;">
                                    <p:commandLink oncomplete="PF('dlgRefeicoes').show();">
                                        <p:graphicImage url="assets/img/icone_cardapio.png"/>
                                    </p:commandLink>
                                </div>
                                <div class="sate-element" style="margin-left: 0px; text-align:center !important;">
                                    <p:commandLink oncomplete="PF('dlgRefeicoes').show();">
                                        <p:outputLabel value="#{localemsgs.Refeicoes}"
                                                       style="color: black;"/>
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>

                        <div class="ui-g">
                            <div class="ui-g-2" style="padding: 0px; margin-bottom: 30px;">
                                <div class="sate-element">
                                </div>
                            </div>
                            <div class="ui-g-2" style="padding: 0px; margin-bottom: 30px;" ref="comercial">
                                <div class="sate-element" style="margin-right: 30px;">
                                    <p:commandLink oncomplete="PF('dlgComercial').show();">
                                        <p:graphicImage url="assets/img/icone_satmob_comercial.png"/>
                                    </p:commandLink>
                                </div>
                                <div class="sate-element" style="margin-right: 30px;">
                                    <p:commandLink oncomplete="PF('dlgComercial').show();">
                                        <p:outputLabel value="#{localemsgs.Comercial}"
                                                       style="color: black;"/>
                                    </p:commandLink>
                                </div>
                            </div>
                            <div class="ui-g-2" style="padding: 0px; margin-bottom: 30px;">
                                <div class="sate-element">
                                </div>
                            </div>
                            <div class="ui-g-2" style="padding: 0px; margin-bottom: 30px;">
                                <div class="sate-element">
                                </div>
                            </div>
                            <div class="ui-g-2" style="padding: 0px; margin-bottom: 30px;" ref="caixa_forte">
                                <div class="sate-element" style="margin-left: 30px;">
                                    <p:commandLink oncomplete="PF('dlgCaixaForte').show();">
                                        <p:graphicImage url="assets/img/icon_caixa_forte.png"/>
                                    </p:commandLink>
                                </div>
                                <div class="sate-element" style="margin-left: 30px;">
                                    <p:commandLink oncomplete="PF('dlgCaixaForte').show();">
                                        <p:outputLabel value="#{localemsgs.CaixaForte}"
                                                       style="color: black;"/>
                                    </p:commandLink>
                                </div>
                            </div>
                            <div class="ui-g-2" style="padding: 0px; margin-bottom: 30px;text-align:center !important;" ref="cadastros">
                                <div class="sate-element" style="margin-right: 30px;">
                                    <p:commandLink oncomplete="PF('dlgCadastro').show();">
                                        <p:graphicImage url="assets/img/icone_cadastros.png"/>
                                    </p:commandLink>
                                </div>
                                <div class="sate-element" style="margin-right: 30px;">
                                    <p:commandLink oncomplete="PF('dlgCadastro').show();">
                                        <p:outputLabel value="#{localemsgs.Cadastros}"
                                                       style="color: black;"/>
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>


                        <div class="ui-g">

                            <div class="ui-g-2" style="padding: 0px;">
                                <div class="sate-element">
                                </div>
                            </div>
                            <div class="ui-g-2" style="padding: 0px;" ref="recursos">
                                <div class="sate-element" style="margin-left: 30px;">
                                    <p:commandLink oncomplete="PF('dlgRH').show();">
                                        <p:graphicImage url="assets/img/icone_satmob_RH_G.png"/>
                                    </p:commandLink>
                                </div>
                                <div class="sate-element" style="margin-left: 30px;">
                                    <p:commandLink oncomplete="PF('dlgRH').show();">
                                        <p:outputLabel value="#{localemsgs.RH}"
                                                       style="color: black;"/>
                                    </p:commandLink>
                                </div>
                            </div>
                            <div class="ui-g-2" style="padding: 0px;">
                                <div class="sate-element">
                                </div>
                            </div>
                            <div class="ui-g-2" style="padding: 0px;">
                                <div class="sate-element">
                                </div>
                            </div>
                            <div class="ui-g-2" style="padding: 0px;" ref="tesouraria">
                                <div class="sate-element" style="margin-right: 30px;">
                                    <p:commandLink oncomplete="PF('dlgTesouraria').show();">
                                        <p:graphicImage url="assets/img/icone_tesouraria.png"/>
                                    </p:commandLink>
                                </div>
                                <div class="sate-element" style="margin-right: 30px;">
                                    <p:commandLink oncomplete="PF('dlgTesouraria').show();">
                                        <p:outputLabel value="#{localemsgs.Tesouraria}"
                                                       style="color: black;"/>
                                    </p:commandLink>
                                </div>
                            </div>
                            <div class="ui-g-2" style="padding: 0px;" ref="cofres">
                                <div class="sate-element" style="margin-right: 60px;">
                                    <p:commandLink oncomplete="PF('dlgCofres').show();" rendered="#{login.nivel eq '9'}">
                                        <p:graphicImage url="assets/img/icone_cofres_gerenciados.png"/>
                                    </p:commandLink>
                                </div>
                                <div class="sate-element" style="margin-right: 60px;">
                                    <p:commandLink oncomplete="PF('dlgCofres').show();" rendered="#{login.nivel eq '9'}">
                                        <p:outputLabel value="#{localemsgs.CofresGerenciados}"
                                                       style="color: black;"/>
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>

                        <div class="ui-g">
                            <div class="ui-g-2" style="padding: 0px;">
                                <div class="sate-element">
                                </div>
                            </div>
                            <div class="ui-g-2" style="padding: 0px;">
                                <div class="sate-element">
                                </div>
                            </div>
                            <div class="ui-g-2" style="padding: 0px;" ref="relatorios">
                                <div class="sate-element" style="margin-right: 20px;">
                                    <p:commandLink oncomplete="PF('dlgRelatorios').show();">
                                        <p:graphicImage url="assets/img/icone_relatorios.png"/>
                                    </p:commandLink>
                                </div>
                                <div class="sate-element" style="margin-right: 20px;">
                                    <p:commandLink oncomplete="PF('dlgRelatorios').show();">
                                        <p:outputLabel value="#{localemsgs.Relatorios}"
                                                       style="color: black;"/>
                                    </p:commandLink>
                                </div>
                            </div>
                            <div class="ui-g-2" style="padding: 0px;" ref="configuracoes">
                                <div class="sate-element" style="margin-left: 10px;">
                                    <p:commandLink oncomplete="PF('dlgConfiguracoes').show();">
                                        <p:graphicImage url="assets/img/icone_configuracoes.png"/>
                                    </p:commandLink>
                                </div>
                                <div class="sate-element" style="margin-left: 10px;">
                                    <p:commandLink oncomplete="PF('dlgConfiguracoes').show();">
                                        <p:outputLabel value="#{localemsgs.Configuracoes}"
                                                       style="color: black;"/>
                                    </p:commandLink>
                                </div>
                            </div>
                            
                            <div class="ui-g-2" style="padding: 0px;" ref="seguranca">
                                <div class="sate-element" style="margin-left: 10px;">
                                    <p:commandLink oncomplete="PF('dlgSeguranca').show();">
                                        <p:graphicImage url="assets/img/icon_usuario_senha.png"/>
                                    </p:commandLink>
                                </div>
                                <div class="sate-element" style="margin-left: 10px;">
                                    <p:commandLink oncomplete="PF('dlgSeguranca').show();">
                                        <p:outputLabel value="#{localemsgs.Seguranca}"
                                                       style="color: black;"/>
                                    </p:commandLink>
                                </div>
                            </div>
                        </div>

                        <div class="ui-g-12" style="padding: 0px; margin-top:18px !important;" ref="botoes-rodape">
                            <div class="sate-element" style="text-align: center !important; width:100%">
                                <center>
                                    <p:commandLink action="#{login.logOut}" id="btnSair" style="width:100px !important;">
                                        <p:graphicImage url="assets/img/icone_sair.png" height="80" width="80" />
                                    </p:commandLink>

                                    <p:commandLink  action="#{login.logOut}" style="color:black; width:100px !important;" id="btnLogout" >
                                        <p:graphicImage url="assets/img/icone_voltar_branco.png" style="height:30px !important;width:30px !important;" /> #{localemsgs.TrocarFilial}
                                    </p:commandLink>
                                </center>
                            </div>
                        </div>
                    </h:form>
                </div>
            </div>

            <h:form id="formRelatoriosPosto" class="form-inline">
                <p:dialog widgetVar="dlgRelatoriosPosto" positionType="absolute"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true" responsive="true"
                          showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgRelatoriosPosto"
                          style="border-top:4px solid #3C8DBC !important; max-width:95% !important;overflow:hidden !important;background-color:#EEE !important;">
                    <script>
                        $(document).ready(function () {
                            //first unbind the original click event
                            PF('dlgRelatoriosPosto').closeIcon.unbind('click');

                            //register your own
                            PF('dlgRelatoriosPosto').closeIcon.click(function (e) {
                                $("#formRelatoriosPosto\\:botaoFechar").click();
                                //should be always called
                                e.preventDefault();
                            });
                        })
                    </script>
                    <p:commandButton widgetVar="botaoFechar" style="display: none"
                                     oncomplete="PF('dlgRelatoriosPosto').hide()" id="botaoFechar">
                        <p:confirm header="#{localemsgs.Confirmacao}" message="#{localemsgs.ConfirmarSaida}" icon="ui-icon-alert" />
                    </p:commandButton>
                    <f:facet name="header">
                        <img src="assets/img/icone_satmob_relatorios.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.Relatorios}" style="color:#022a48" />
                    </f:facet>
                    <p:panel id="relatorio" style="background-color: transparent" styleClass="cadastrar">
                        <p:confirmDialog global="true">
                            <p:commandButton value="#{localemsgs.Sim}" type="button" styleClass="ui-confirmdialog-yes" icon="ui-icon-check"/>
                            <p:commandButton value="#{localemsgs.Nao}" type="button" styleClass="ui-confirmdialog-no" icon="ui-icon-close"/>
                        </p:confirmDialog>

                        <p:panelGrid columns="2" columnClasses="ui-grid-col-3,ui-grid-col-9"
                                     styleClass="ui-panelgrid-blank" style="width: 100%;">

                            <p:outputLabel for="posto" value="#{localemsgs.PstServ}: " indicateRequired="false"/>
                            <p:autoComplete id="posto" styleClass="cliente2" placeholder="#{localemsgs.PstServ}"
                                            style="width: 100%" forceSelection="true" minQueryLength="3"
                                            completeMethod="#{postoservico.listarPostosSimples}" required="true"
                                            requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.PstServ}"
                                            var="pst" itemLabel="#{pst.local}" itemValue="#{pst}" scrollHeight="250">
                                <o:converter converterId="omnifaces.ListIndexConverter" list="#{postoservico.listaPosto}" />
                                <p:ajax event="itemSelect" listener="#{postoservico.selecionarPostoRelatorio}" update="tabelaRelatorio msgs"/>
                            </p:autoComplete>


                            <h:outputText value="#{localemsgs.BuscarPeriodo}: "/>
                        </p:panelGrid>

                        <div style="float: left; width: 100%">
                            <p:outputLabel for="dataRelatorio1" value="#{localemsgs.DataInicial}: "/>
                            <p:spacer width="10px"/>
                            <p:inputMask id="dataRelatorio1" value="#{postoservico.dataRelatorio1}" mask="99/99/9999" style="width: 90px;"/>

                            <p:spacer width="10px"/>

                            <p:outputLabel for="dataRelatorio2" value="#{localemsgs.DataFinal}: "/>
                            <p:spacer width="10px"/>
                            <p:inputMask id="dataRelatorio2" value="#{postoservico.dataRelatorio2}" mask="99/99/9999" style="width: 90px;"/>

                            <p:spacer width="10px"/>

                            <p:commandLink title="#{localemsgs.Pesquisar}" update="tabelaRelatorio msgs" action="#{postoservico.listarRelatorios}">
                                <p:graphicImage url="assets/img/icone_redondo_pesquisar.png" height="30"/>
                            </p:commandLink>
                        </div>
                        <div style="float: left; width: 100%">
                            <p:dataTable id="tabelaRelatorio" value="#{postoservico.relatorios}"
                                         style="font-size: 12px" var="relatorio" rowKey="#{relatorio.tmktdetpst.sequencia}"
                                         styleClass="tabela" scrollHeight="200"
                                         resizableColumns="true" scrollable="true" selectionMode="single" scrollWidth="100%"
                                         emptyMessage="#{localemsgs.SemRegistros}"
                                         selection="#{postoservico.relatorio}">
                                <p:ajax event="rowDblselect" listener="#{postoservico.dbClickRelatorioMenu}"
                                        update="formExibicaoRelatorio:relatorio"/>
                                <p:column headerText="#{localemsgs.Data}" style="width: 75px">
                                    <h:outputText value="#{relatorio.tmktdetpst.data}" title="#{relatorio.tmktdetpst.data}"
                                                  converter="conversorData"
                                                  style="#{relatorio.tmktdetpst.situacao eq 'OK' ? 'color: green' :
                                                           relatorio.tmktdetpst.situacao eq 'PD' ? 'color: red' : 'color: blue' }"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Hora}" style="width: 75px">
                                    <h:outputText value="#{relatorio.tmktdetpst.hora}" title="#{relatorio.tmktdetpst.hora}"
                                                  converter="conversorHora"
                                                  style="#{relatorio.tmktdetpst.situacao eq 'OK' ? 'color: green' :
                                                           relatorio.tmktdetpst.situacao eq 'PD' ? 'color: red' : 'color: blue' }"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Descricao}" style="width: 200px">
                                    <h:outputText value="#{relatorio.tmktdetpst.historico}" title="#{relatorio.tmktdetpst.historico}"
                                                  style="#{relatorio.tmktdetpst.situacao eq 'OK' ? 'color: green' :
                                                           relatorio.tmktdetpst.situacao eq 'PD' ? 'color: red' : 'color: blue' }"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Detalhes}" style="width: 200px">
                                    <h:outputText value="#{relatorio.tmktdetpst.detalhes}" title="#{relatorio.tmktdetpst.detalhes}"
                                                  style="white-space: normal; #{relatorio.tmktdetpst.situacao eq 'OK' ? 'color: green' :
                                                                                relatorio.tmktdetpst.situacao eq 'PD' ? 'color: red' : 'color: blue' }"/>
                                </p:column>
                                <p:column headerText="#{localemsgs.Operador}" style="width: 60px">
                                    <h:outputText value="#{relatorio.tmktdetpst.operador}" title="#{relatorio.tmktdetpst.operador}"
                                                  style="#{relatorio.tmktdetpst.situacao eq 'OK' ? 'color: green' :
                                                           relatorio.tmktdetpst.situacao eq 'PD' ? 'color: red' : 'color: blue' }"/>
                                </p:column>
                            </p:dataTable>
                        </div>
                    </p:panel>
                </p:dialog>
            </h:form>

            <h:form id="formExibicaoRelatorio">
                <p:dialog widgetVar="dlgExibicaoRelatorio" positionType="absolute"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true" responsive="true"
                          showEffect="drop" hideEffect="drop" closeOnEscape="false" id="dlgEditar"
                          style="border-top:4px solid #3C8DBC !important; max-width:95% !important;overflow:hidden !important;background-color:#EEE !important;">
                    <f:facet name="header">
                        <img src="assets/img/icone_satmob_relatorios.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.Relatorio}" style="color:#022a48" />
                    </f:facet>
                    <p:panel id="relatorio" style="background-color: transparent" styleClass="cadastrar">
                        <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10" layout="grid" styleClass="ui-panelgrid-blank">
                            <p:outputLabel for="local" value="#{localemsgs.PstServ}"/>
                            <p:inputText id="local" value="#{postoservico.relatorio.pstserv.local}" readonly="true" style="width: 100%"/>

                            <p:outputLabel for="endereco" value=""/>
                            <p:inputText id="endereco" value="#{postoservico.relatorio.clientes.NRed} - #{postoservico.relatorio.clientes.nome}" readonly="true" style="width: 100%"/>

                            <p:outputLabel for="endereco2" value=""/>
                            <p:inputText id="endereco2" value="#{postoservico.relatorio.clientes.ende} - #{postoservico.relatorio.clientes.bairro}" readonly="true" style="width: 100%"/>

                            <p:outputLabel for="endereco3" value=""/>
                            <p:inputText id="endereco3" value="#{postoservico.enderecoRelatorio}" readonly="true" style="width: 100%"/>

                            <p:outputLabel for="funcionario" value="#{localemsgs.Vigilante}"/>
                            <p:inputText id="funcionario" value="#{postoservico.relatorio.funcionario}" readonly="true" style="width: 100%"/>

                            <p:outputLabel for="ocorrencia" value="#{localemsgs.Ocorrencia}"/>
                            <p:inputText id="ocorrencia" value="#{postoservico.relatorio.tmktdetpst.historico}" readonly="true" style="width: 100%"/>

                            <p:outputLabel for="detalhes" value="#{localemsgs.Detalhes}"/>
                            <p:inputTextarea rows="4" id="detalhes" value="#{postoservico.relatorio.tmktdetpst.detalhes}" scrollHeight="200"
                                             readonly="true" style="width: 100%" title="#{postoservico.relatorio.tmktdetpst.detalhes}"/>
                        </p:panelGrid>

                        <p:panelGrid columns="4" columnClasses="ui-grid-col-2,ui-grid-col-4,ui-grid-col-2,ui-grid-col-4" layout="grid" styleClass="ui-panelgrid-blank">
                            <p:outputLabel for="data" value="#{localemsgs.Data}"/>
                            <p:inputText id="data" value="#{postoservico.relatorio.tmktdetpst.data}" converter="conversorData" readonly="true" style="width: 100%"/>

                            <p:outputLabel for="hora" value="#{localemsgs.Hora}"/>
                            <p:inputText id="hora" value="#{postoservico.relatorio.tmktdetpst.hora}" converter="conversorHora" readonly="true" style="width: 100%"/>
                        </p:panelGrid>

                        <p:panelGrid columns="3" columnClasses="ui-grid-col-2,ui-grid-col-8,ui-grid-col-2" layout="grid" styleClass="ui-panelgrid-blank">

                            <h:outputText value="#{localemsgs.Fotos} " rendered="#{postoservico.relatorio.fotos.size() gt 0}"/>

                            <h:outputText value="#{localemsgs.QtdFotos}: #{postoservico.relatorio.fotos.size()}" rendered="#{postoservico.relatorio.fotos.size() gt 0}"/>

                            <h:outputText value=" " rendered="#{postoservico.relatorio.fotos.size() gt 0}"/>

                            <p:commandLink action="#{postoservico.voltarFotoRelatorio}" rendered="#{postoservico.relatorio.fotos.size() gt 1}"
                                           update="formExibicaoRelatorio:fotoRelatorio msgs">
                                <p:graphicImage url="../assets/img/botao_anterior.png" height="20" title="#{localemsgs.FotoAnterior}"/>
                            </p:commandLink>
                            <h:outputText value=" " rendered="#{postoservico.relatorio.fotos.size() eq 1}"/>

                            <p:panel style="width: 100%; text-align: center; background: transparent" rendered="#{postoservico.relatorio.fotos.size() gt 0}">
                                <p:lightBox style=" text-align: center;" id="fotoPosto">
                                    <h:outputLink value="#{postoservico.fotoRelatorio}"
                                                  title="#{postoservico.relatorio.pstserv.local}">
                                        <h:graphicImage value="#{postoservico.fotoRelatorio}" id="fotoRelatorio" style="height: 400px;"/>
                                    </h:outputLink>
                                </p:lightBox>
                                <p:panelGrid columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                             layout="grid" styleClass="ui-panelgrid-blank">
                                    <p:commandButton id="rotateLeft" icon="ui-icon-arrowreturnthick-1-w" style="float: right; width: 30px"
                                                     onclick="rotateLeft(event)" rendered="#{not empty postoservico.fotoRelatorio}"/>
                                    <p:commandButton id="rotateRight" icon="ui-icon-arrowreturnthick-1-e" style="float: left; width: 30px"
                                                     onclick="rotateRight(event)" rendered="#{not empty postoservico.fotoRelatorio}"/>
                                    <script type="text/javascript">
                                        rotate = 0;
                                        function rotateLeft(e) {
                                            e.preventDefault();
                                            if (rotate === -360) {
                                                rotate = 0;
                                            }

                                            rotate = rotate + -90;
                                            var img = document.getElementById("formExibicaoRelatorio:fotoRelatorio");
                                            var h = img.clientHeight;
                                            var w = img.clientWidth;

                                            img.style.transform = "rotate(" + rotate + "deg)";
                                            img.height = w;
                                            img.width = h;
                                        }
                                        ;

                                        function rotateRight(e) {
                                            e.preventDefault();
                                            if (rotate === 360) {
                                                rotate = 0;
                                            }

                                            rotate = rotate + 90;
                                            var img = document.getElementById("formExibicaoRelatorio:fotoRelatorio");
                                            var h = img.clientHeight;
                                            var w = img.clientWidth;

                                            img.style.transform = "rotate(" + rotate + "deg)";
                                            img.height = w;
                                            img.width = h;
                                        }
                                        ;
                                    </script>
                                </p:panelGrid>
                            </p:panel>

                            <p:commandLink action="#{postoservico.avancarFotoRelatorio}" rendered="#{postoservico.relatorio.fotos.size() gt 1}"
                                           update="formExibicaoRelatorio:fotoRelatorio msgs">
                                <p:graphicImage url="../assets/img/botao_proximo.png" height="20" title="#{localemsgs.ProximaFoto}"/>
                            </p:commandLink>
                            <h:outputText value=" " rendered="#{postoservico.relatorio.fotos.size() eq 1}"/>

                            <h:outputText value="#{localemsgs.Mapa}"/>

                        </p:panelGrid>
                        <div class="ui-grid-row">
                            <p:gmap center="#{postoservico.coordenadas}" zoom="15" model="#{postoservico.pin}"
                                    type="ROADMAP" style="width:100%;height:210px;"/>
                        </div>
                    </p:panel>
                </p:dialog>
            </h:form>

            <!-- Cadastro -->
            <h:form id="formCadastros" >
                <p:dialog widgetVar="dlgCadastro" header="#{localemsgs.Cadastros}" id="dlgCadastro" positionType="absolute" onShow="PF('dlgCadastro').initPosition();"
                          modal="true" closable="true" resizable="false" dynamic="true"  draggable="false"
                          showEffect="drop" hideEffect="drop" responsive="true" styleClass="dialogo"
                          style="border-top:4px solid #3C8DBC !important; overflow:hidden !important;background-color:#EEE !important">
                    <p:panel id="cadastro" style="background-color: transparent" styleClass="panelMenu">
                        <p:panelGrid columns="2" columnClasses="ui-grid-col-3,ui-grid-col-3,ui-grid-col-3,ui-grid-col-3"
                                     styleClass="ui-panelgrid-blank" style="width: 100%; text-align: center;">
                            <h:panelGrid columns="1" style="width: 100%; text-align: center;">
                                <p:commandLink onclick="return ValidarAcesso('59901','cadastros/filiais.xhtml?faces-redirect=true','#{login.retornaPermissao('59901')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_filiais.png" width="120" />
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('59901','cadastros/filiais.xhtml?faces-redirect=true','#{login.retornaPermissao('59901')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.Filiais}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="width: 100%; text-align: center;">
                                <p:commandLink onclick="return ValidarAcesso('50106','cadastros/veiculos.xhtml?faces-redirect=true','#{login.retornaPermissao('50106')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_satmob_veiculos.png" width="120" />
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('50106','cadastros/veiculos.xhtml?faces-redirect=true','#{login.retornaPermissao('50106')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.Veiculos}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>

                        </p:panelGrid>
                    </p:panel>
                </p:dialog>
            </h:form>

            <!-- Refeicoes -->
            <h:form id="formRefeicoes" >
                <p:dialog widgetVar="dlgRefeicoes" header="#{localemsgs.Refeicoes}" id="dlgCadastro" positionType="absolute" onShow="PF('dlgCadastro').initPosition();"
                          modal="true" closable="true" resizable="false" dynamic="true"  draggable="false"
                          showEffect="drop" hideEffect="drop" responsive="true" styleClass="dialogo"
                          style="border-top:4px solid #3C8DBC !important; overflow:hidden !important;background-color:#EEE !important">
                    <p:panel id="cadastro" style="background-color: transparent" styleClass="panelMenu">
                        <p:panelGrid columns="3" columnClasses="ui-grid-col-3,ui-grid-col-3,ui-grid-col-3,ui-grid-col-3"
                                     styleClass="ui-panelgrid-blank" style="width: 100%; text-align: center;">
                            <h:panelGrid columns="1" style="width: 100%; text-align: center;">
                                <p:commandLink onclick="return ValidarAcesso('102201','refeicoes/cardapio.xhtml?faces-redirect=true','#{login.retornaPermissao('102201')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_satmob_cardapio1.png" width="120" />
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('102201','refeicoes/cardapio.xhtml?faces-redirect=true','#{login.retornaPermissao('102201')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.Cardapios}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="width: 100%; text-align: center;">
                                <p:commandLink onclick="return ValidarAcesso('102201','refeicoes/cardapios.xhtml?faces-redirect=true','#{login.retornaPermissao('102201')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_satmob_cardapiododia.png" width="120" />
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('102201','refeicoes/cardapios.xhtml?faces-redirect=true','#{login.retornaPermissao('102201')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.CardapioDia}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="width: 100%; text-align: center;">
                                <p:commandLink onclick="return ValidarAcesso('10101','refeicoes/pedidos_refeicao.xhtml?faces-redirect=true','#{login.retornaPermissao('10101')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_pedidos.png" width="100px" />
                                </p:commandLink>

                                <p:commandLink onclick="return ValidarAcesso('10101','refeicoes/pedidos_refeicao.xhtml?faces-redirect=true','#{login.retornaPermissao('10101')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.Pedidos}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>
                        </p:panelGrid>
                    </p:panel>
                </p:dialog>
            </h:form>

            <!-- COMERCIAL -->
            <h:form id="formComercial" >
                <p:dialog widgetVar="dlgComercial" header="#{localemsgs.Comercial}" positionType="absolute" onShow="PF('dlgComercial').initPosition();"
                          modal="true" closable="true" resizable="false" dynamic="true"  draggable="false"
                          showEffect="drop" hideEffect="drop" responsive="true" styleClass="dialogo"
                          style="border-top:4px solid #3C8DBC !important; overflow:hidden !important;background-color:#EEE !important">
                    <p:panel id="cadastro" style="background-color: transparent" styleClass="panelMenu">
                        <p:panelGrid columns="4" columnClasses="ui-grid-col-3,ui-grid-col-3,ui-grid-col-3,ui-grid-col-3"
                                     styleClass="ui-panelgrid-blank" style="width: 100%; text-align: center;">
                            <h:panelGrid columns="1" style="width: 100%; text-align: center">
                                <p:commandLink onclick="return ValidarAcesso('20101','comercial/contatos.xhtml?faces-redirect=true','#{login.retornaPermissao('20101')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_satmob_contatos_G.png" width="120px" />
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('20101','comercial/contatos.xhtml?faces-redirect=true','#{login.retornaPermissao('20101')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.Contatos}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="width: 100%; text-align: center">
                                <p:commandLink onclick="return ValidarAcesso('50201','comercial/produtos.xhtml?faces-redirect=true','#{login.retornaPermissao('50201')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_satmob_produtosG.png" width="120px" />
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('50201','comercial/produtos.xhtml?faces-redirect=true','#{login.retornaPermissao('50201')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.Produtos}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="width: 100%; text-align: center">
                                <p:commandLink onclick="return ValidarAcesso('20113','comercial/contratos.xhtml?faces-redirect=true','#{login.retornaPermissao('20113')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_satmob_contratos_G.png" width="120px" />
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('20113','comercial/contratos.xhtml?faces-redirect=true','#{login.retornaPermissao('20113')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.Contratos}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="width: 100%; text-align: center">
                                <p:commandLink onclick="return ValidarAcesso('20102','comercial/proposta.xhtml?faces-redirect=true','#{login.retornaPermissao('20102')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_satmob_proposta.png" width="120px" />
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('20102','comercial/proposta.xhtml?faces-redirect=true','#{login.retornaPermissao('20102')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.Propostas}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>


                            <h:panelGrid columns="1" style="width: 100%; text-align: center">
                                <p:commandLink onclick="return ValidarAcesso('50101','comercial/clientes.xhtml?faces-redirect=true','#{login.retornaPermissao('50101')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_clientes.png" width="120px" />
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('50101','comercial/clientes.xhtml?faces-redirect=true','#{login.retornaPermissao('50101')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.Clientes}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>
                        </p:panelGrid>
                    </p:panel>
                </p:dialog>
            </h:form>


            <!-- FATURAMENTO -->
            <h:form id="formFaturamento">
                <p:dialog widgetVar="dlgFaturamento" header="#{localemsgs.Faturamento}" positionType="absolute" onShow="PF('dlgFaturamento').initPosition();"
                          modal="true" closable="true" resizable="false" dynamic="true"  draggable="false"
                          showEffect="drop" hideEffect="drop" responsive="true" styleClass="dialogo"
                          style="border-top:4px solid #3C8DBC !important; overflow:hidden !important;background-color:#EEE !important">
                    <p:panel id="cadastro" style="background-color: transparent; height: 100%; overflow:hidden !important" styleClass="panelMenu">
                        <p:panelGrid columns="2" columnClasses="ui-grid-col-6 ui-grid-col-6"
                                     styleClass="ui-panelgrid-blank" style="width: 100%; text-align: center;overflow:hidden !important">
                            <h:panelGrid columns="1" style="width: 100%; text-align: center;">
                                <p:commandLink onclick="return ValidarAcesso('30401','faturamento/nfiscal.xhtml?faces-redirect=true','#{login.retornaPermissao('30401')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_notafiscal_g.png" width="100px" />
                                </p:commandLink>                                
                            </h:panelGrid>
                            <h:panelGrid columns="1" style="width: 100%; text-align: center">
                                <p:commandLink onclick="return ValidarAcesso('30111','faturamento/os_vig.xhtml?faces-redirect=true','#{login.retornaPermissao('30111')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_satmob_fopag_G.png" width="120px" />
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('30111','faturamento/os_vig.xhtml?faces-redirect=true','#{login.retornaPermissao('30111')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.OS}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="width: 100%; text-align: center; margin-top: 20px !Important" rendered="#{login.transpCacamba}">
                                <p:commandLink action="faturamento/container_fat.xhtml?faces-redirect=true">
                                    <p:graphicImage url="assets/img/icone_contas_receber.png" width="100px" />
                                </p:commandLink>
                                <p:commandLink action="faturamento/container_fat.xhtml?faces-redirect=true">
                                    <p:outputLabel value="#{localemsgs.ResumoCaixa}"
                                                   style="color:#022a48; margin-top: 6px !Important"/>
                                </p:commandLink>
                            </h:panelGrid>
                        </p:panelGrid>
                    </p:panel>
                </p:dialog>
            </h:form>

            <!-- Operacoes -->
            <h:form id="formOperacoes">
                <p:dialog header="#{localemsgs.Operacoes}" widgetVar="dlgOperacoes" positionType="absolute" onShow="PF('dlgOperacoes').initPosition();"
                          modal="true" closable="true" resizable="false" dynamic="true"  draggable="false"
                          showEffect="drop" hideEffect="drop" responsive="true" styleClass="dialogo"
                          style="border-top:4px solid #3C8DBC !important; overflow:hidden !important;background-color:#EEE !important">
                    <p:panel id="cadastro" style="background-color: transparent" styleClass="panelMenu">
                        <p:panelGrid columns="4" columnClasses="ui-grid-col-3,ui-grid-col-3,ui-grid-col-3,ui-grid-col-3"
                                     styleClass="ui-panelgrid-blank" style="width: 100%; text-align: center;overflow:hidden !important">

                            <h:panelGrid columns="1" style="min-width: 100%; text-align: center">
                                <p:commandLink onclick="return ValidarAcesso('10207','operacoes/dashboard.xhtml?faces-redirect=true','#{login.retornaPermissao('10207')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_dashboard.png" width="100px" />
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('10207','operacoes/dashboard.xhtml?faces-redirect=true','#{login.retornaPermissao('10207')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.Dashboard}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="min-width: 100%; text-align: center">
                                <p:commandLink onclick="return ValidarAcesso('10221','operacoes/escaladia.xhtml?faces-redirect=true','#{login.retornaPermissao('10221')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_escaladodia.png" width="100px" />
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('10221','operacoes/escaladia.xhtml?faces-redirect=true','#{login.retornaPermissao('10221')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.EscalaDia}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="min-width: 100%; text-align: center">
                                <p:commandLink onclick="return ValidarAcesso('10311','operacoes/supervisao.xhtml?faces-redirect=true','#{login.retornaPermissao('10311')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_supervisao.png" width="100px" />
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('10311','operacoes/supervisao.xhtml?faces-redirect=true','#{login.retornaPermissao('10311')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.Supervisao}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="min-width: 100%; text-align: center">
                                <p:commandLink onclick="return ValidarAcesso('10201','operacoes/rotasupervisao.xhtml?faces-redirect=true','#{login.retornaPermissao('10201')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_rotasdesupervisao.png" width="100px" />
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('10201','operacoes/rotasupervisao.xhtml?faces-redirect=true','#{login.retornaPermissao('10201')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.RotasSup}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="min-width: 100%; text-align: center">
                                <p:commandLink onclick="return ValidarAcesso('10211','operacoes/mapa_direccion.xhtml?faces-redirect=true','#{login.retornaPermissao('10211')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icones_satmob_carroforte.png" width="100px" />
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('10211','operacoes/mapa_direccion.xhtml?faces-redirect=true','#{login.retornaPermissao('10211')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.RotasValores}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>


                            <h:panelGrid columns="1" style="min-width: 100%; text-align: center">
                                <p:commandLink action="operacoes/pedidos.xhtml?faces-redirect=true" onclick="return ValidarAcesso('10101','operacoes/pedidos.xhtml?faces-redirect=true','#{login.retornaPermissao('10101')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_pedidos.png" width="100px" />
                                </p:commandLink>

                                <p:commandLink action="operacoes/pedidos.xhtml?faces-redirect=true" onclick="return ValidarAcesso('10101','operacoes/pedidos.xhtml?faces-redirect=true','#{login.retornaPermissao('10101')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.Pedidos}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="min-width: 100%; text-align: center">
                                <p:commandLink onclick="return ValidarAcesso('10201','operacoes/rotasvalores.xhtml?faces-redirect=true','#{login.retornaPermissao('10201')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_escalasrotas.png" width="100px" />
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('10201','operacoes/rotasvalores.xhtml?faces-redirect=true','#{login.retornaPermissao('10201')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.Rotas}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="min-width: 100%; text-align: center">
                                <p:commandLink action="#{valores.abrirFormularioAssinaturas}">
                                    <p:graphicImage url="assets/img/icone_satmob_fopag_G.png" width="95px" />
                                </p:commandLink>
                                <p:commandLink action="#{valores.abrirFormularioAssinaturas}" >
                                    <p:outputLabel value="#{localemsgs.Guias}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="min-width: 100%; text-align: center">
                                <p:commandLink onclick="return ValidarAcesso('10207','operacoes/coaf.xhtml?faces-redirect=true','#{login.retornaPermissao('10207')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_satmob_rendimentos_G.png" width="100px" style='margin-top:15px !important' />
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('10207','operacoes/coaf.xhtml?faces-redirect=true','#{login.retornaPermissao('10207')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.CoafComunic}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>
                            
                            <h:panelGrid columns="1" style="min-width: 100%; text-align: center">
                                <p:commandLink onclick="return ValidarAcesso('10201','operacoes/direccion_completo.xhtml?faces-redirect=true','#{login.retornaPermissao('10201')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_satmob_listaderota.png" width="100px" style='margin-top:15px !important' />
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('10201','operacoes/direccion_completo.xhtml?faces-redirect=true','#{login.retornaPermissao('10201')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.TodasRotas}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="min-width: 100%; text-align: center">
                                <p:commandLink onclick="return ValidarAcesso('10311','operacoes/operacoes_servico.xhtml?faces-redirect=true','#{login.retornaPermissao('10311')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_satmob_operacoesdeservico.png" width="100px" style='margin-top:15px !important' />
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('10311','operacoes/operacoes_servico.xhtml?faces-redirect=true','#{login.retornaPermissao('10311')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.OperacoesServicos}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="min-width: 100%; text-align: center; margin-top: 18px !important">
                                <p:commandLink onclick="return ValidarAcesso('10201','operacoes/ultima_localizacao.xhtml?faces-redirect=true','#{login.retornaPermissao('10201')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_ultima_localizacao_supervisor.png" height="90px" />
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('10201','operacoes/ultima_localizacao.xhtml?faces-redirect=true','#{login.retornaPermissao('10201')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.UltimaLocalizacaoSupervisor}"
                                                   style="color:#022a48; margin-top: 6px !important"/>
                                </p:commandLink>
                            </h:panelGrid>
                        </p:panelGrid>
                    </p:panel>
                </p:dialog>
            </h:form>


            <!-- Guias/Assinatura -->
            <h:form id="impressao">
                <p:dialog widgetVar="dlgImprimir" positionType="absolute" id="dlgImprimir" onShow="PF('dlgImprimir').initPosition();"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                          showEffect="drop" hideEffect="drop" closeOnEscape="false" responsive="true" styleClass="dialogo">
                    <f:facet name="header">
                        <h:outputText value="#{localemsgs.Guia}" style="float: left"/>

                        <!-- Botão para Imprimir -->
                         <p:commandLink title="#{localemsgs.Imprimir}"
                                        style="position: absolute; width:10px !important; right: 60px; top: 0px">
                             <i class="fa fa-print"></i>
                             <p:printer target="guiaimpressa"/> 
                         </p:commandLink>

                         <!-- Botão para Enviar Email -->
                        <p:commandLink title="#{localemsgs.EnviarEmail}"
                            style="position: absolute; width:10px !important; right: 40px; top: 0px; display: none"
                            action="#{valores.enviarEmail}">
                            <i class="fa fa-envelope"></i>
                        </p:commandLink>

                    </f:facet>


                    <p:panel class="guiaimpressa" styleClass="guiaimpressa"
                             style="padding:12px !important; border:thin solid #DDD !important; overflow:auto !important;max-height: 400px !important">
                        <h:outputText id="guiaimpressa"
                                      value="#{valores.html}" escape="false"/>
                    </p:panel>
                </p:dialog>
                
            </h:form>

            <h:form id="formGuiasAssina" style="overflow:hidden !important;  text-align: center !important;">
                <p:dialog header="#{localemsgs.PesquisarGuia}" widgetVar="dlgGuiasAssina" positionType="absolute"
                          modal="true" closable="true" resizable="false" dynamic="true"  draggable="false"
                          showEffect="drop" hideEffect="drop" responsive="true" styleClass="dialogo"
                          style="border-top:4px solid #3C8DBC !important; overflow:hidden !important;background-color:#EEE !important">
                    <p:panel id="cadastro" style="background-color: transparent" styleClass="panelMenu">
                        <div class="row" style="padding: 0px; margin: 0px">
                            <div class="col-md-3 col-sm-3 col-xs-6" style="padding: 4px">
                                <label>#{localemsgs.Numero}</label>
                                <p:inputText value="#{valores.guiaPesquisa.guiaString}" style="width: 100%; text-align: center" styleClass="form-control"></p:inputText>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-6" style="padding: 4px">
                                <label>#{localemsgs.numero_serie}</label>
                                <p:inputText value="#{valores.guiaPesquisa.serie}" style="width: 100%; text-align: center" styleClass="form-control" maxlength="4"></p:inputText>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-6 divBt" style="padding: 28px 4px 4px 4px">
                                <p:commandButton id="btPesquisar" styleClass="btn btn-primary" style="width: 100%; height: 33px; padding: 4px !important; outline: none" value="#{localemsgs.Pesquisar}" actionListener="#{valores.pesquisarGuiaSerieAssinada}" process="cadastro" update="msgs cadastro" oncomplete="$(window).resize()"></p:commandButton>
                            </div>
                            <div class="col-md-3 col-sm-3 col-xs-6 divBt" style="padding: 28px 4px 4px 4px; display: #{valores.guiaPesquisa.operador ne null and valores.guiaPesquisa.operador ne ''?'': 'none'}">
                                <p:commandButton id="btImprimirGuia" styleClass="btn btn-success" style="width: 100%; height: 33px; padding: 4px !important; outline: none" value="#{localemsgs.Imprimir}" actionListener="#{valores.imprimeGuiaAssinada}" process="cadastro" update="msgs cadastro"></p:commandButton>
                            </div>
                        </div>
                        <p:panel rendered="#{valores.guiaPesquisa.operador ne null and valores.guiaPesquisa.operador ne ''}">
                            <div class="col-md-6 col-sm-6 col-xs-12" style="padding: 5px; display: #{valores.guiaPesquisa.assRemetente ne null and valores.guiaPesquisa.assRemetente ne ''? '': 'none'}">
                                <div class="col-md-12 col-dm-12 col-xs-12" style="border: thin solid #DDD; padding: 10px; height: 200px; background-color: #FFF;">
                                    <div class="col-md-12 col-dm-12 col-xs-12" onclick="TirarFoto('#{valores.guiaPesquisa.assRemetenteImagem ne null and valores.guiaPesquisa.assRemetenteImagem ne ''? 'N':'S'}', 'ori');" style="background-position: center center; background-repeat: no-repeat; background-size: #{valores.guiaPesquisa.assRemetenteImagem ne null and valores.guiaPesquisa.assRemetenteImagem ne ''? 'contain':'auto'}; height: 100%; background-image: url(#{valores.guiaPesquisa.assRemetenteImagem ne null and valores.guiaPesquisa.assRemetenteImagem ne ''? valores.guiaPesquisa.assRemetenteImagem: 'https://mobile.sasw.com.br/SatMobWeb/assets/img/icone_capturarfoto.png'})"></div>                                    
                                </div>
                                <label style="padding: 3px; margin-top: 5px; background-color: lightyellow; border: thin solid orangered; border-radius: 15px; width: 100%; text-align: center; font-weight: bold; color: orangered; text-shadow: 1px 1px #FFF;">#{valores.guiaPesquisa.assRemetente}</label>
                            </div>
                            <div class="col-md-6 col-sm-6 col-xs-12" onclick="TirarFoto('#{valores.guiaPesquisa.assDestinatarioImagem ne null and valores.guiaPesquisa.assDestinatarioImagem ne ''? 'N':'S'}', 'dst');" style="padding: 5px; display: #{valores.guiaPesquisa.assDestinatario ne null and valores.guiaPesquisa.assDestinatario ne ''? '': 'none'}">
                                <div class="col-md-12 col-dm-12 col-xs-12" style="border: thin solid #DDD; padding: 10px; height: 200px; background-color: #FFF;">
                                    <div class="col-md-12 col-dm-12 col-xs-12"  style="background-position: center center; background-repeat: no-repeat; background-size: #{valores.guiaPesquisa.assDestinatarioImagem ne null and valores.guiaPesquisa.assDestinatarioImagem ne ''? 'contain':'auto'};  height: 100%; background-image: url(#{valores.guiaPesquisa.assDestinatarioImagem ne null and valores.guiaPesquisa.assDestinatarioImagem ne ''? valores.guiaPesquisa.assDestinatarioImagem: 'https://mobile.sasw.com.br/SatMobWeb/assets/img/icone_capturarfoto.png'})"></div>                                    
                                </div>
                                <label style="padding: 3px; margin-top: 5px; background-color: lightyellow; border: thin solid orangered; border-radius: 15px; width: 100%; text-align: center; font-weight: bold; color: orangered; text-shadow: 1px 1px #FFF;">#{valores.guiaPesquisa.assDestinatario}</label>
                            </div>
                        </p:panel>
                    </p:panel>
                    <p:panel id="pnlUpload" style="display: none">
                        <p:remoteCommand name="rc" 
                                         process="txtDescricaoArq" />

                        <p:inputText id="txtDescricaoArq" value="#{valores.infoWindows}"></p:inputText>

                        <p:fileUpload id="uploadFotosRelatorio"
                                      fileUploadListener="#{valores.HandleFileUpload}"
                                      allowTypes="/(\.|\/)(png|jpe?g|gif|bmp|PNG|JPE?G|GIF|BMP)$/"
                                      auto="true" multiple="false"
                                      process="@this txtDescricaoArq"
                                      invalidFileMessage="#{localemsgs.ArquivoInvalido}"
                                      dragDropSupport="false" fileLimitMessage="#{localemsgs.QtdArquivosInvalida}"
                                      update="msgs cadastro" skinSimple="true" previewWidth="10"
                                      style="width:10px; height:10px;"></p:fileUpload>
                    </p:panel>
                </p:dialog>
            </h:form>

            <!-- Cofres -->
            <h:form id="formCofres" style="overflow:hidden !important;  text-align: center !important;">
                <p:dialog header="#{localemsgs.CofresGerenciados}" widgetVar="dlgCofres" positionType="absolute" onShow="PF('dlgCofres').initPosition();"
                          modal="true" closable="true" resizable="false" dynamic="true"  draggable="false"
                          showEffect="drop" hideEffect="drop" responsive="true" styleClass="dialogo"
                          style="border-top:4px solid #3C8DBC !important; overflow:hidden !important;background-color:#EEE !important">
                    <p:panel id="cadastro" style="background-color: transparent" styleClass="panelMenu">
                        <p:panelGrid columns="2" columnClasses="ui-grid-col-3,ui-grid-col-3,ui-grid-col-3,ui-grid-col-3"
                                     styleClass="ui-panelgrid-blank" style="width: 100%; text-align: center;">
                            <h:panelGrid columns="1" style="min-width: 100%; text-align: center">
                                <p:commandLink actionListener="#{login.abrirDashboardCofreMenu}" onclick="return ValidarAcesso('40414','cofre/dashboard_geral.xhtml?faces-redirect=true','#{login.retornaPermissao('40414')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_dashboard.png" width="100px" />
                                </p:commandLink>
                                <p:commandLink actionListener="#{login.abrirDashboardCofreMenu}" onclick="return ValidarAcesso('40414','cofre/dashboard_geral.xhtml?faces-redirect=true','#{login.retornaPermissao('40414')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.Dashboard}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="min-width: 100%; text-align: center">
                                <p:commandLink actionListener="#{login.abrirCofreMenu}" onclick="return ValidarAcesso('40414','cofre/cofre.xhtml?faces-redirect=true','#{login.retornaPermissao('40414')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_cofres_gerenciados.png" width="100px" />
                                </p:commandLink>
                                <p:commandLink actionListener="#{login.abrirCofreMenu}" onclick="return ValidarAcesso('40414','cofre/cofre.xhtml?faces-redirect=true','#{login.retornaPermissao('40414')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.CofresGerenciados}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>
                            
                            <h:panelGrid columns="1" style="min-width: 100%; text-align: center">
                                <p:commandLink actionListener="#{login.abrirCofreMovMenu}" onclick="return ValidarAcesso('40414','cofre/cofres_movimentacao.xhtml?faces-redirect=true','#{login.retornaPermissao('40414')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_cofres_gerenciados.png" width="100px" />
                                </p:commandLink>
                                <p:commandLink actionListener="#{login.abrirCofreMovMenu}" onclick="return ValidarAcesso('40414','cofre/cofres_movimentacao.xhtml?faces-redirect=true','#{login.retornaPermissao('40414')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.CofresGerenciadosMov}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>
                        </p:panelGrid>
                    </p:panel>
                </p:dialog>
            </h:form>

            <!-- Tesouraria -->
            <h:form id="formTesouraria" class="form-inline">
                <p:dialog header="#{localemsgs.Tesouraria}" widgetVar="dlgTesouraria" positionType="absolute" onShow="PF('dlgTesouraria').initPosition();"
                          modal="true" closable="true" resizable="false" dynamic="true"  draggable="false"
                          showEffect="drop" hideEffect="drop" responsive="true" styleClass="dialogo"
                          style="border-top:4px solid #3C8DBC !important; overflow:hidden !important;background-color:#EEE !important">
                    <p:panel id="cadastro" style="background-color: transparent" styleClass="panelMenu">
                        <p:panelGrid columns="2" columnClasses="ui-grid-col-3,ui-grid-col-3,ui-grid-col-3,ui-grid-col-3"
                                     styleClass="ui-panelgrid-blank" style="width: 100%; text-align: center;">

                            <h:panelGrid columns="1" style="width: 100%; text-align: center">
                                <p:commandLink onclick="return ValidarAcesso('10261','tesouraria/tes_entradas.xhtml?faces-redirect=true','#{login.retornaPermissao('10261')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_tesouraria_entradas.png"/>
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('10261','tesouraria/tes_entradas.xhtml?faces-redirect=true','#{login.retornaPermissao('10261')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.Entradas}"
                                                   style="color: #022a48"/>
                                </p:commandLink>
                            </h:panelGrid>
                            <h:panelGrid columns="1" style="width: 100%; text-align: center">
                                <p:commandLink onclick="return ValidarAcesso('20262','tesouraria/tes_saidas.xhtml?faces-redirect=true','#{login.retornaPermissao('20262')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_tesouraria_saidas.png"/>
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('20262','tesouraria/tes_saidas.xhtml?faces-redirect=true','#{login.retornaPermissao('20262')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.Saidas}"
                                                   style="color: #022a48"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="min-width: 100%; text-align: center">
                                <p:commandLink onclick="return ValidarAcesso('10208','tesouraria/dashboard.xhtml?faces-redirect=true','#{login.retornaPermissao('10208')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_dashboard.png" width="100px" />
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('10208','tesouraria/dashboard.xhtml?faces-redirect=true','#{login.retornaPermissao('10208')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.Dashboard}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="min-width: 100%; text-align: center">
                                <p:commandLink onclick="return ValidarAcesso('10208','tesouraria/produtividade.xhtml?faces-redirect=true','#{login.retornaPermissao('10208')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_area_produtividade.png" width="100px" />
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('10208','tesouraria/produtividade.xhtml?faces-redirect=true','#{login.retornaPermissao('10208')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.Produtividade}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>
                            <!--                            <h:panelGrid columns="1" style="width: 100%; text-align: center">
                                                            <p:commandLink action="tesouraria/tes_automatizada.xhtml?faces-redirect=true">
                                                                <p:graphicImage url="assets/img/icone_tesourariaautomatizada.png"/>
                                                            </p:commandLink>
                                                            <p:commandLink action="tesouraria/tes_automatizada.xhtml?faces-redirect=true">
                                                                <p:outputLabel value="#{localemsgs.Automatizada}"
                                                                               style="color: #022a48"/>
                                                            </p:commandLink>
                                                        </h:panelGrid>-->
                        </p:panelGrid>
                    </p:panel>
                </p:dialog>
            </h:form>

            <!-- PortalRH -->
            <h:form id="formRH" >
                <p:dialog header="#{localemsgs.RH}" widgetVar="dlgRH" positionType="absolute" onShow="PF('dlgRH').initPosition();"
                          modal="true" closable="true" resizable="false" dynamic="true"  draggable="false"
                          showEffect="drop" hideEffect="drop" responsive="true" styleClass="dialogo"
                          style="border-top:4px solid #3C8DBC !important; overflow:hidden !important;background-color:#EEE !important">
                    <p:panel id="cadastro" style="background-color: transparent" styleClass="panelMenu">
                        <p:panelGrid columns="4" columnClasses="ui-grid-col-3,ui-grid-col-3,ui-grid-col-3,ui-grid-col-3"
                                     styleClass="ui-panelgrid-blank" style="width: 100%; text-align: center;">
                            <h:panelGrid columns="1" style="width: 100%; text-align: center">
                                <p:commandLink onclick="return ValidarAcesso('40111','recursoshumanos/funcion.xhtml?faces-redirect=true','#{login.retornaPermissao('40111')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_funcionarios.png" width="120px" />
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('40111','recursoshumanos/funcion.xhtml?faces-redirect=true','#{login.retornaPermissao('40111')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.Funcion}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="width: 100%; text-align: center">
                                <p:commandLink onclick="return ValidarAcesso('40112','recursoshumanos/pstserv.xhtml?faces-redirect=true','#{login.retornaPermissao('40112')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_postosdeservico.png" width="120" />
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('40112','recursoshumanos/pstserv.xhtml?faces-redirect=true','#{login.retornaPermissao('40112')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.PostoServicos}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="width: 100%; text-align: center">
                                <p:commandLink onclick="return ValidarAcesso('40103','recursoshumanos/pessoas.xhtml?faces-redirect=true','#{login.retornaPermissao('40103')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_pessoas.png" width="120px" />
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('40103','recursoshumanos/pessoas.xhtml?faces-redirect=true','#{login.retornaPermissao('40103')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.Pessoas}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1"  style="width: 100%; text-align: center">
                                <p:commandLink actionListener="#{pontos.setPersistencia(login.pp)}"
                                               action="#{pontos.buscaPeriodos}"
                                               update="folhadeponto msgs" >
                                    <p:graphicImage url="assets/img/icone_satmob_fopag_G.png" width="120px" />
                                </p:commandLink>
                                <p:commandLink actionListener="#{pontos.setPersistencia(login.pp)}"
                                               action="#{pontos.buscaPeriodos}"
                                               update="folhadeponto msgs">
                                    <p:outputLabel value="#{localemsgs.FolhaDePonto}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1"  style="width: 100%; text-align: center">
                                <p:commandLink actionListener="#{contracheque.setPersistencia(login.pp,login.pool)}"
                                               action="#{contracheque.buscaPeriodos}"
                                               update="contracheques msgs" id="corpvsBtnCC">
                                    <p:graphicImage url="assets/img/icone_satmob_contracheque_G.png" width="120px" />
                                </p:commandLink>
                                <p:commandLink actionListener="#{contracheque.setPersistencia(login.pp,login.pool)}"
                                               action="#{contracheque.buscaPeriodos}"
                                               update="contracheques msgs" id="corpvsBtnCCLink">
                                    <p:outputLabel value="#{localemsgs.ContraCheque}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>





                            <h:panelGrid columns="1"  style="width: 100%; text-align: center">
                                <p:commandLink onclick="return ValidarAcesso('60118','recursoshumanos/log_contracheque.xhtml?faces-redirect=true','#{login.retornaPermissao('60118')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_logs_contracheque_satmob.png" height="90px" style="margin-top: 15px" />
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('60118','recursoshumanos/log_contracheque.xhtml?faces-redirect=true','#{login.retornaPermissao('60118')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.LogContraCheque}"
                                                   style="color:#022a48; margin-top: 15px"/>
                                </p:commandLink>
                            </h:panelGrid>







                            <h:panelGrid columns="1"  style="width: 100%; text-align: center">
                                <p:commandLink actionListener="#{funcoesadm.setPersistencia(login.pp)}"
                                               action="#{funcoesadm.logsAdm}"
                                               update="adm msgs">
                                    <p:graphicImage url="assets/img/icone_satmob_contracheque_funcaoadm_G.png" width="120px" />
                                </p:commandLink>
                                <p:commandLink actionListener="#{funcoesadm.setPersistencia(login.pp)}"
                                               action="#{funcoesadm.logsAdm}"
                                               update="adm msgs">
                                    <p:outputLabel value="#{localemsgs.FuncoesAdm}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1"  style="width: 100%; text-align: center">
                                <p:commandLink onclick="return ValidarAcesso('40300','recursoshumanos/esocial.xhtml?faces-redirect=true','#{login.retornaPermissao('40300')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_satmob_esocialG.png" class="es" width="120px" />
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('40300','recursoshumanos/esocial.xhtml?faces-redirect=true','#{login.retornaPermissao('40300')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.ESocial}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1"  style="width: 100%; text-align: center">
                                <p:commandLink onclick="return ValidarAcesso('40210','recursoshumanos/registrospontos.xhtml?faces-redirect=true','#{login.retornaPermissao('40210')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_satmob_fopag_G.png" class="es" width="120px" />
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('40210','recursoshumanos/registrospontos.xhtml?faces-redirect=true','#{login.retornaPermissao('40210')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.RegistroPresenca}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid  columns="1"  style="width: 100%; text-align: center" rendered="#{login.ano ne null}">

                                <p:commandLink actionListener="#{rendimentos.setPersistencia(login.pp)}"
                                               action="#{rendimentos.imprimirRendimentos}" update="msgs"
                                               ajax="false" rendered="#{login.ano ne null}">
                                    <p:graphicImage url="assets/img/icone_satmob_rendimentos_G.png" width="120px"/>
                                </p:commandLink>
                                <p:commandLink actionListener="#{rendimentos.setPersistencia(login.pp)}"
                                               action="#{rendimentos.imprimirRendimentos}" update="msgs"
                                               ajax="false" rendered="#{login.ano ne null}">
                                    <p:outputLabel value="#{localemsgs.InformeRendimentos} #{login.ano}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="width: 100%; text-align: center">
                                <p:commandLink onclick="return ValidarAcesso('40210','recursoshumanos/grafico.xhtml?faces-redirect=true','#{login.retornaPermissao('40210')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_satmob_graficosG.png" width="120" />
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('40210','recursoshumanos/grafico.xhtml?faces-redirect=true','#{login.retornaPermissao('40210')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.Grafico}" style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>
                        </p:panelGrid>
                    </p:panel>
                </p:dialog>
            </h:form>

            <h:form id="adm">
                <p:dialog header="#{localemsgs.FuncoesAdm}" widgetVar="dlgFuncoesAdm" draggable="false" positionType="absolute"
                          modal="true" closable="true" resizable="false" responsive="true" dynamic="true" styleClass="dlg40"
                          width="440" showEffect="drop" hideEffect="drop" style="border-top:4px solid #3C8DBC !important; max-width:95% !important;overflow:hidden !important;">
                    <p:accordionPanel styleClass="accordion" activeIndex="null" id="accordionAdm">
                        <p:tab title="#{localemsgs.Matricula}" id="matricula">
                            <p:accordionPanel styleClass="nestedAccordion">
                                <p:tab title="#{localemsgs.FolhaDePonto}">
                                    <table style="text-align: center; width: 100%">
                                        <c:forEach var="fp" items="#{funcoesadm.folhasp}">
                                            <tr>
                                                <td>
                                                    <h:outputText value="#{fp}" styleClass="textoazul"/>
                                                </td>
                                            </tr>
                                        </c:forEach>
                                    </table>
                                </p:tab>
                                <p:tab title="#{localemsgs.Contracheque}">
                                    <table style="text-align: center; width: 100%">
                                        <c:forEach var="cc" items="#{funcoesadm.cCheque}">
                                            <tr>
                                                <td>
                                                    <h:outputText value="#{cc}" styleClass="textoazul"/>
                                                </td>
                                            </tr>
                                        </c:forEach>
                                    </table>
                                </p:tab>
                            </p:accordionPanel>
                        </p:tab>
                        <p:tab title="#{localemsgs.Mensagens}" id="mensagens" rendered="#{login.nivel eq '9'}">
                            <div style="text-align: center">
                                <h:outputText value="Assunto" styleClass="textoazul"/>
                                <p:inputText value="#{funcoesadm.assunto}" style="width: 97%"/>

                                <h:outputText value="Aviso" styleClass="textoazul"/>
                                <p:textEditor value="#{funcoesadm.aviso}" styleClass="textoazul" style="width: 97%" height="90">
                                    <f:facet name="toolbar">
                                        <span class="ql-formats">
                                            <button class="ql-bold"></button>
                                            <button class="ql-italic"></button>
                                            <button class="ql-underline"></button>
                                            <button class="ql-strike"></button>
                                            <button class="ql-list" value="bullet"></button>
                                            <select class="ql-size"></select>
                                            <select class="ql-align"></select>
                                        </span>
                                    </f:facet>
                                </p:textEditor>

                                <p:commandLink style="width: 130px !important;"
                                               update="msgs" actionListener="#{funcoesadm.enviarMensagem}"
                                               action="#{login.atualizaMensagem}">
                                    <p:graphicImage url="assets/img/icone_confirmar.png" width="40px" height="40px"/>
                                </p:commandLink>
                            </div>
                        </p:tab>
                        <p:tab title="#{localemsgs.ListaDatas}" id="listadatas" rendered="#{login.nivel eq '9'}">
                            <p:panelGrid columns="2" styleClass="pnl" columnClasses="ui-grid-col-6,ui-grid-col-6">
                                <p:outputLabel for="matr" value="#{localemsgs.Matricula}:"/>
                                <p:inputText id="matr" value="#{funcoesadm.matr}"/>

                                <p:outputLabel for="dini" value="Data inicial:"/>
                                <p:inputMask id="dini" value="#{funcoesadm.dataIni}" mask="99/99/9999"/>

                                <p:outputLabel for="dfim" value="Data final:"/>
                                <p:inputMask id="dfim" value="#{funcoesadm.dataFim}" mask="99/99/9999"/>

                                <h:panelGrid columns="1"  style="width: 100%; text-align: center">
                                    <p:commandLink style="width: 130px !important;"
                                                   action="#{funcoesadm.listaDatasCC}" oncomplete="PF('dlgDatas').show()"
                                                   update="msgs datas:info">
                                        <p:graphicImage url="assets/img/icone_satmob_contracheque_P.png" width="40px" height="40px"/>
                                    </p:commandLink>
                                    <p:commandLink style="width: 130px !important;" value="#{localemsgs.Contracheque}"
                                                   action="#{funcoesadm.listaDatasCC}" oncomplete="PF('dlgDatas').show()"
                                                   update="msgs datas:info"/>
                                </h:panelGrid>

                                <h:panelGrid columns="1"  style="width: 100%; text-align: center">
                                    <p:commandLink style="width: 130px !important;"
                                                   action="#{funcoesadm.listaDatasFP}" oncomplete="PF('dlgDatas').show()"
                                                   update="msgs datas:info">
                                        <p:graphicImage url="assets/img/icone_satmob_fopag_P.png" width="40px" height="40px"/>
                                    </p:commandLink>
                                    <p:commandLink style="width: 130px !important;"
                                                   value="#{localemsgs.FolhaDePonto}"
                                                   action="#{funcoesadm.listaDatasFP}" oncomplete="PF('dlgDatas').show()"
                                                   update="msgs datas:info"/>
                                </h:panelGrid>

                            </p:panelGrid>
                        </p:tab>
                    </p:accordionPanel>
                </p:dialog>
            </h:form>

            <!-- FUNÇÕES ADM RH -->
            <h:form id="datas">
                <p:dialog header="#{localemsgs.FuncoesAdm}" widgetVar="dlgDatas" draggable="false" positionType="absolute"
                          modal="true" closable="true" resizable="false" responsive="true" dynamic="true" styleClass="dlg"
                          width="440" showEffect="drop" hideEffect="drop" style="border-top:4px solid #3C8DBC !important; max-width:95% !important;overflow:hidden !important;">
                    <p:panelGrid columns="2" styleClass="pnl">
                        <p:outputLabel for="matr" value="#{localemsgs.Matricula}:"/>
                        <p:inputText id="matr" value="#{funcoesadm.matr}"/>

                        <p:outputLabel for="dini" value="Data inicial:"/>
                        <p:inputMask id="dini" value="#{funcoesadm.dataIni}" mask="99/99/9999"/>

                        <p:outputLabel for="dfim" value="Data final:"/>
                        <p:inputMask id="dfim" value="#{funcoesadm.dataFim}" mask="99/99/9999"/>

                        <p:commandButton value="#{localemsgs.Contracheque}" style="width: 130px !important;"
                                         action="#{funcoesadm.listaDatasCC}"
                                         update="msgs info" class="custom-button"/>

                        <p:commandButton value="#{localemsgs.FolhaDePonto}" style="width: 130px !important;"
                                         action="#{funcoesadm.listaDatasFP}"
                                         update="msgs info" class="custom-button"/>
                    </p:panelGrid>
                    <p:scrollPanel id="info" mode="native" style="width:100%;height:200px">
                        <table style="text-align: center; width: 100%">
                            <c:forEach var="dados" items="#{funcoesadm.dados}">
                                <tr>
                                    <td>
                                        <h:outputText value="#{dados}" styleClass="textoazul"/>
                                    </td>
                                </tr>
                            </c:forEach>
                        </table>
                    </p:scrollPanel>
                </p:dialog>
            </h:form>

            <!-- CONTRACHEQUE -->
            <h:form id="contracheques">
                <p:dialog header="#{localemsgs.Contracheque}" widgetVar="dlgContracheque" draggable="false"
                          modal="true" closable="true" resizable="false" dynamic="true"
                          width="440" showEffect="drop" hideEffect="drop" style="border-top:4px solid #3C8DBC !important; max-width:95% !important;overflow:hidden !important;">
                    <p:accordionPanel value="#{contracheque.fpperiodos}" var="fpperiodos" dynamic="true" styleClass="accordion">
                        <p:ajax event="tabChange" listener="#{contracheque.buscarFpmensais}" update="msgs" />
                        <p:tab title="#{fpperiodos.dtInicioF}" id="periodocontracheque">
                            <table style="text-align: center; width: 100%">
                                <tr>
                                    <td>
                                        <h:outputText class="textoazul" value="#{localemsgs.SemRegistros}"
                                                      rendered="#{contracheque.fpmensais.isEmpty()}"/>
                                    </td>
                                </tr>
                                <c:forEach var="fp" items="#{contracheque.fpmensais}">
                                    <tr>
                                        <td>
                                            <p:commandLink value="#{fp.tipoFpFormatado}" target="_blank"
                                                           action="#{contracheque.imprimirContracheque(fp)}"
                                                           update="msgs" styleClass="linkazul"
                                                           ajax="false"/>
                                        </td>
                                    </tr>
                                </c:forEach>
                            </table>
                        </p:tab>
                    </p:accordionPanel>
                </p:dialog>
            </h:form>

            <!-- FOLHA DE PONTO -->
            <h:form id="folhadeponto">
                <p:dialog header="#{localemsgs.FolhaDePonto}" widgetVar="dlgFolhadePonto" draggable="false"
                          modal="true" closable="true" resizable="false" dynamic="true"
                          width="440" showEffect="drop" hideEffect="drop" style="border-top:4px solid #3C8DBC !important; max-width:95% !important;overflow:hidden !important;">
                    <table style="text-align: center; width: 100%">
                        <h:outputText class="textoazul" value="#{localemsgs.SemRegistros}" rendered="#{pontos.periodos.isEmpty()}"/>
                        <c:forEach var="periodos" items="#{pontos.periodos}">
                            <tr>
                                <td>
                                    <p:commandLink value="#{periodos.dt_Ini} a #{periodos.dt_Fim}"
                                                   action="#{pontos.imprimirFolhaDePonto(periodos)}"
                                                   update="msgs" styleClass="linkazul" target="_blank"
                                                   ajax="false"/>
                                </td>
                            </tr>
                        </c:forEach>
                    </table>
                </p:dialog>
            </h:form>

            <!-- Relatorios -->
            <h:form id="formRelatorios" class="form-inline">
                <p:dialog header="#{localemsgs.Relatorios}" widgetVar="dlgRelatorios" positionType="absolute" onShow="PF('dlgRelatorios').initPosition();"
                          modal="true" closable="true" resizable="false" dynamic="true"  draggable="false"
                          showEffect="drop" hideEffect="drop" responsive="true" styleClass="dialogo"
                          style="border-top:4px solid #3C8DBC !important; overflow:hidden !important;background-color:#EEE !important">
                    <p:panel id="cadastro" style="background-color: transparent" styleClass="panelMenu">
                        <p:panelGrid columns="3" columnClasses="ui-grid-col-3,ui-grid-col-3,ui-grid-col-3,ui-grid-col-3"
                                     styleClass="ui-panelgrid-blank" style="width: 100%; text-align: center;">
                            <h:panelGrid columns="1" style="width: 100%; text-align: center">
                                <p:commandLink 
                                    action="#{login.acessarRecurso('541100','relatorio/portal.xhtml?faces-redirect=true')}" 
                                    update="msgs">
                                    <p:graphicImage url="assets/img/icone_satmob_contratosG.png" width="120px" />
                                </p:commandLink>
                                <p:commandLink                                  
                                    action="#{login.acessarRecurso('541100','relatorio/portal.xhtml?faces-redirect=true')}" 
                                    update="msgs">
                                    <p:outputLabel value="#{localemsgs.SatMobEW}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="width: 100%; text-align: center">
                                <p:commandLink 
                                    action="#{login.acessarRecurso('541113','relatorio/inspecoes.xhtml?faces-redirect=true')}" 
                                    update="msgs">
                                    <p:graphicImage url="assets/img/icones_satmob_insp_postos_G.png" width="120px" />
                                </p:commandLink>
                                <p:commandLink 
                                    action="#{login.acessarRecurso('541113','relatorio/inspecoes.xhtml?faces-redirect=true')}" 
                                    update="msgs">
                                    <p:outputLabel value="#{localemsgs.Inspecoes}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="width: 100%; text-align: center">
                                <p:commandLink actionListener="#{login.novoRelatorioKM}" oncomplete="PF('dlgRelatorioKMPrestador').show()"
                                               update="panelRelatorioKMPrestador">
                                    <p:graphicImage url="assets/img/icone_satmob_contatos_G.png" width="120px" />
                                </p:commandLink>
                                <p:commandLink actionListener="#{login.novoRelatorioKM}" oncomplete="PF('dlgRelatorioKMPrestador').show()"
                                               update="panelRelatorioKMPrestador">
                                    <p:outputLabel value="#{localemsgs.DSEIndividual}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="width: 100%; text-align: center">
                                <p:commandLink 
                                    action="#{login.acessarRecurso('30109','relatorio/prefatura.xhtml?faces-redirect=true')}" 
                                    update="msgs">
                                    <p:graphicImage url="assets/img/icone_Dashboard_rota.png" width="120px" />
                                </p:commandLink>
                                <p:commandLink 
                                    action="#{login.acessarRecurso('30109','relatorio/prefatura.xhtml?faces-redirect=true')}" 
                                    update="msgs">
                                    <p:outputLabel value="#{localemsgs.PreFatura}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>                                
                            </h:panelGrid>
                            <h:panelGrid columns="1" style="width: 100%; text-align: center">
                                <p:commandLink 
                                    action="#{login.acessarRecurso('30109','relatorio/prefaturaclifat.xhtml?faces-redirect=true')}" 
                                    update="msgs">
                                    <p:graphicImage url="assets/img/icone_Dashboard_cliente.png" width="120px" />
                                </p:commandLink>
                                <p:commandLink 
                                    action="#{login.acessarRecurso('30109','relatorio/prefaturaclifat.xhtml?faces-redirect=true')}" 
                                    update="msgs">
                                    <p:outputLabel value="#{localemsgs.PreFaturaCliente}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>
                            <h:panelGrid columns="1" style="width: 100%; text-align: center">
                                <p:commandLink 
                                    action="#{login.acessarRecurso('60118','relatorio/centralalerta.xhtml?faces-redirect=true')}" 
                                    update="msgs">
                                    <p:graphicImage url="assets/img/icone_satmob_contratosG.png" width="120px" />
                                </p:commandLink>
                                <p:commandLink 
                                    action="#{login.acessarRecurso('60118','relatorio/centralalerta.xhtml?faces-redirect=true')}" 
                                    update="msgs">                                    
                                    <p:outputLabel value="#{localemsgs.CentralAlerta}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>
                            <h:panelGrid columns="1" style="width: 100%; text-align: center">
                                <p:commandLink 
                                    action="#{login.acessarRecurso('60118','dashboard/secsaude.xhtml?faces-redirect=true')}" 
                                    update="msgs">                                    
                                    <p:graphicImage url="assets/img/icone_dashboard.png" width="120px" />
                                </p:commandLink>
                                <p:commandLink 
                                    action="#{login.acessarRecurso('60118','dashboard/secsaude.xhtml?faces-redirect=true')}" 
                                    update="msgs">                                    
                                    <p:outputLabel value="#{localemsgs.Dashboard}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="width: 100%; text-align: center; margin-top: 18px !important">
                                <p:commandLink 
                                    action="#{login.acessarRecurso('101101','relatorio/contas_receber.xhtml?faces-redirect=true')}" 
                                    update="msgs">                                    
                                    <p:graphicImage url="assets/img/icone_contas_receber.png" height="90px" />
                                </p:commandLink>
                                <p:commandLink 
                                    action="#{login.acessarRecurso('101101','relatorio/contas_receber.xhtml?faces-redirect=true')}" 
                                    update="msgs">                                    
                                    <p:outputLabel value="#{localemsgs.ContasReceber}"
                                                   style="color:#022a48; margin-top: 10px !important"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="width: 100%; text-align: center; margin-top: 18px !important">
                                <p:commandLink 
                                    action="#{login.acessarRecurso('301001','relatorio/contas_pagar.xhtml?faces-redirect=true')}" 
                                    update="msgs">                                    
                                    <p:graphicImage url="assets/img/icone_contas_pagar.png" height="90px" />
                                </p:commandLink>
                                <p:commandLink 
                                    action="#{login.acessarRecurso('301001','relatorio/contas_pagar.xhtml?faces-redirect=true')}" 
                                    update="msgs">                                    
                                    <p:outputLabel value="#{localemsgs.ContasPagar}"
                                                   style="color:#022a48; margin-top: 10px !important"/>
                                </p:commandLink>
                            </h:panelGrid>
                        </p:panelGrid>
                    </p:panel>
                </p:dialog>

                <p:dialog header="#{localemsgs.DSEIndividual}" widgetVar="dlgRelatorioKMPrestador" draggable="false"
                          modal="true" closable="true" resizable="false" dynamic="true" styleClass="dlg"
                          width="440" showEffect="drop" hideEffect="drop" style="border-top:4px solid #3C8DBC !important; max-width:95% !important;">
                    <p:panel id="panelRelatorioKMPrestador">
                        <p:panelGrid columns="2" styleClass="pnl">

                            <p:outputLabel for="dini" value="Data inicio:"/>
                            <p:inputMask id="dini" value="#{login.dataRelatorioKMIni}" mask="99/99/9999"
                                         required="true" label="#{localemsgs.Data}"
                                         requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.DtInicial}"
                                         style="width: 100%"
                                         maxlength="8" placeholder="#{mascaras.padraoData}"
                                         converter="conversorData"/>

                            <p:outputLabel for="dfim" value="Data final:"/>
                            <p:inputMask id="dfim" value="#{login.dataRelatorioKMFim}" mask="99/99/9999"
                                         required="true" label="#{localemsgs.Data}"
                                         requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.DtFinal}"
                                         style="width: 100%"
                                         maxlength="8" placeholder="#{mascaras.padraoData}"
                                         converter="conversorData">
                                <p:ajax process="panelRelatorioKMPrestador" update="msgs panelRelatorioKMPrestador" partialSubmit="true"
                                        listener="#{login.selecionarDataFimRelatorioKM}" event="blur"/>
                            </p:inputMask>

                            <p:outputLabel for="prestador" value="#{localemsgs.Colaborador}: " indicateRequired="false" rendered="#{login.existeDataRelatorioKMFim}"/>
                            <p:autoComplete id="prestador" styleClass="cliente2" rendered="#{login.existeDataRelatorioKMFim}"
                                            style="width: 100%" forceSelection="true" value="#{login.prestadorRelatorio}"
                                            completeMethod="#{login.listaPrestadoresRelatorioKM}" required="true"
                                            requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Colaborador}"
                                            var="emp" itemLabel="#{emp.nome}" itemValue="#{emp}" scrollHeight="250">
                                <o:converter converterId="omnifaces.ListIndexConverter" list="#{login.prestadoresRelatoriokM}" />
                                <p:ajax event="itemSelect" update="msgs panelRelatorioKMPrestador" listener="#{login.selecionarPrestadoresRelatorioKM}"/>
                            </p:autoComplete>

                            <p:commandLink title="#{localemsgs.Download}" update="msgs"
                                           ajax="false" rendered="#{login.existePessoaRelatorioKMm}"
                                           actionListener="#{login.gerarRelatorioKMPrestador}">
                                <p:graphicImage url="assets/img/icone_pdf.png" height="40"/>
                                <p:fileDownload value="#{login.arquivoDownload}"/>
                            </p:commandLink>
                        </p:panelGrid>
                    </p:panel>
                </p:dialog>

            </h:form>

            <!-- Configuracoes -->
            <h:form id="formConfiguracoes" class="form-inline">
                <p:dialog header="#{localemsgs.Configuracoes}" widgetVar="dlgConfiguracoes" positionType="absolute" onShow="PF('dlgConfiguracoes').initPosition();"
                          modal="true" closable="true" resizable="false" dynamic="true"  draggable="false"
                          showEffect="drop" hideEffect="drop" responsive="true" styleClass="dialogo"
                          style="border-top:4px solid #3C8DBC !important; overflow:hidden !important;background-color:#EEE !important">
                    <p:panel id="cadastro" style="background-color: transparent" styleClass="panelMenu">
                        <p:panelGrid columns="#{login.pp.empresa ne 'SASW'?'2':'3'}" columnClasses="ui-grid-col-3,ui-grid-col-3,ui-grid-col-3,ui-grid-col-3"
                                     styleClass="ui-panelgrid-blank" style="width: 100%; text-align: center;">
                            <h:panelGrid columns="1" style="width: 100%; text-align: center">
                                <p:commandLink onclick="return ValidarAcesso('60118','configuracoes/acessos.xhtml?faces-redirect=true','#{login.retornaPermissao('60118')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_usuarios.png"/>
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('60118','configuracoes/acessos.xhtml?faces-redirect=true','#{login.retornaPermissao('60118')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.Usuarios}"
                                                   style="color: #022a48"/>
                                </p:commandLink>
                            </h:panelGrid>
                            
                            <h:panelGrid columns="1" style="width: 100%; text-align: center">
                                <p:commandLink onclick="return ValidarAcesso('60118','configuracoes/pessoa_acesso.xhtml?faces-redirect=true','#{login.retornaPermissao('60118')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_usuarios.png"/>
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('60118','configuracoes/pessoa_acesso.xhtml?faces-redirect=true','#{login.retornaPermissao('60118')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.PessoaAcesso}"
                                                   style="color: #022a48"/>
                                </p:commandLink>
                            </h:panelGrid>
                            
                            <h:panelGrid columns="1" style="width: 100%; text-align: center">
                                <p:commandLink onclick="return ValidarAcesso('60118','dlgImportar','#{login.retornaPermissao('60118')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_importador.png"/>
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('60118','dlgImportar','#{login.retornaPermissao('60118')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.Importador}"
                                                   style="color: #022a48"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="width: 100%; text-align: center">
                                <p:commandLink onclick="return ValidarAcesso('60118','dlgExportacao','#{login.retornaPermissao('60118')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_exportador.png"/>
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('60118','dlgExportacao','#{login.retornaPermissao('60118')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.Exportador}"
                                                   style="color: #022a48"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="width: 100%; text-align: center" rendered="#{login.pp.empresa eq 'SASW'}">
                                <p:commandLink onclick="return ValidarAcesso('60118','dlgOutrasBases','#{login.retornaPermissao('60118')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/logo.png"/>
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('60118','dlgOutrasBases','#{login.retornaPermissao('60118')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.OutrasBases}"
                                                   style="color: #022a48"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="width: 100%; text-align: center" rendered="#{login.pp.empresa eq 'SASW'}">
                                <p:commandLink onclick="return ValidarAcesso('60118','configuracoes/novoparametro.xhtml?faces-redirect=true','#{login.retornaPermissao('60118')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icon-database.png" style="width: 95px !important; margin-bottom: 2px !Important" />
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('60118','configuracoes/novoparametro.xhtml?faces-redirect=true','#{login.retornaPermissao('60118')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.Parametro}"
                                                   style="color: #022a48"/>
                                </p:commandLink>
                            </h:panelGrid>
                        </p:panelGrid>
                    </p:panel>
                </p:dialog>
            </h:form>
            
            
            
             <!-- Segurança -->
            <h:form id="formSeguranca" class="form-inline">
                <p:dialog header="#{localemsgs.Seguranca}" widgetVar="dlgSeguranca" positionType="absolute" onShow="PF('dlgSeguranca').initPosition();"
                          modal="true" closable="true" resizable="false" dynamic="true"  draggable="false"
                          showEffect="drop" hideEffect="drop" responsive="true" styleClass="dialogo"
                          style="border-top:4px solid #3C8DBC !important; overflow:hidden !important;background-color:#EEE !important">
                    <p:panel id="cadastro" style="background-color: transparent" styleClass="panelMenu">
                        <p:panelGrid columns="3" columnClasses="ui-grid-col-3,ui-grid-col-3,ui-grid-col-3,ui-grid-col-3"
                                     styleClass="ui-panelgrid-blank" style="width: 100%; text-align: center;">
                            <h:panelGrid columns="1" style="width: 100%; text-align: center">
                                <p:commandLink onclick="return ValidarAcesso('90101','seguranca/autorizacao_acesso.xhtml?faces-redirect=true','#{login.retornaPermissao('90101')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_satmob_contracheque_funcaoadm_G.png"/>
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('90101','seguranca/autorizacao_acesso.xhtml?faces-redirect=true','#{login.retornaPermissao('90101')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.AutorizacaoAcesso}"
                                                   style="color: #022a48"/>
                                </p:commandLink>
                            </h:panelGrid>
                            
                        </p:panelGrid>
                    </p:panel>
                </p:dialog>
            </h:form>

            <!-- Caixa Forte -->
            <h:form id="formCaixaForte" class="form-inline">
                <p:dialog header="#{localemsgs.CaixaForte}" widgetVar="dlgCaixaForte" positionType="absolute" onShow="PF('dlgCaixaForte').initPosition();"
                          modal="true" closable="true" resizable="false" dynamic="true"  draggable="false"
                          showEffect="drop" hideEffect="drop" responsive="true" styleClass="dialogo"
                          style="border-top:4px solid #3C8DBC !important; overflow:hidden !important;background-color:#EEE !important">
                    <p:panel id="cadastro" style="background-color: transparent" styleClass="panelMenu">
                        <p:panelGrid columns="2" columnClasses="ui-grid-col-3,ui-grid-col-3,ui-grid-col-3,ui-grid-col-3"
                                     styleClass="ui-panelgrid-blank" style="width: 100%; text-align: center;">
                            <h:panelGrid columns="1" style="width: 100%; text-align: center">
                                <p:commandLink onclick="return ValidarAcesso('10251','dlgCaixaForteCustodia','#{login.retornaPermissao('10251')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icones_custodia_CAIXAFORTE.png" width="120px"/>
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('10251','dlgCaixaForteCustodia','#{login.retornaPermissao('10251')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.Custodia}"
                                                   style="color: #022a48"/>
                                </p:commandLink>
                            </h:panelGrid>
                            <h:panelGrid columns="1" style="width: 100%; text-align: center">
                                <p:commandLink onclick="return ValidarAcesso('10252','cxforte/entrada.xhtml?faces-redirect=true','#{login.retornaPermissao('10252')?'S':'N'}')">
                                    <p:graphicImage url="assets/img/icone_entradacaixaforte.png" width="120px" />
                                </p:commandLink>
                                <p:commandLink onclick="return ValidarAcesso('10252','cxforte/entrada.xhtml?faces-redirect=true','#{login.retornaPermissao('10252')?'S':'N'}')">
                                    <p:outputLabel value="#{localemsgs.EntradaCxForte}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>                            
                        </p:panelGrid>
                    </p:panel>
                </p:dialog>
            </h:form>

            <h:form id="formOutrasBases" >
                <p:dialog widgetVar="dlgOutrasBases" header="#{localemsgs.OutrasBases}" positionType="absolute"
                          modal="true" closable="true" resizable="false" dynamic="true" draggable="false" responsive="true"
                          showEffect="drop" hideEffect="drop" height="310" styleClass="dialogo"
                          style="border-top:4px solid #3C8DBC !important; max-width:95% !important;overflow:hidden !important; background-color:#EEE !important">
                    <p:panel id="cadastrar" style="background-color: transparent" class="cadastrar">
                        <p:panelGrid columns="2" columnClasses="ui-grid-col-2,ui-grid-col-10"
                                     layout="grid" styleClass="ui-panelgrid-blank">
                            <p:outputLabel for="empresa" value="#{localemsgs.Tipo}: " indicateRequired="false"/>
                            <p:autoComplete id="empresa" styleClass="cliente2"
                                            style="width: 100%" forceSelection="true"
                                            completeMethod="#{login.listaEmpresas}" required="true"
                                            requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Tipo}"
                                            var="emp" itemLabel="#{emp}" itemValue="#{emp}" scrollHeight="250">
                                <p:ajax event="itemSelect" update="msgs" listener="#{login.selecionarNovaEmpresa}"/>
                                <p:watermark for="empresa" value="#{localemsgs.empresa}" />
                            </p:autoComplete>

                            <p:autoComplete id="subFil" completeMethod="#{login.listarFils}"
                                            label="#{localemsgs.Filial}" forceSelection="true"
                                            requiredMessage="#{localemsgs.Obrigatorio}: #{localemsgs.Filial}" scrollHeight="200"
                                            var="fils" itemValue="#{fils}" itemLabel="#{fils.descricao}">
                                <o:converter converterId="omnifaces.ListIndexConverter" list="#{login.novasFils}" />
                                <p:ajax event="itemSelect" listener="#{login.selecionarNovaFilial}"/>
                            </p:autoComplete>

                        </p:panelGrid>
                    </p:panel>
                </p:dialog>
            </h:form>

            <!-- Caixa Forte - Custódia -->
            <h:form id="formCaixaForteCustodia">
                <p:dialog widgetVar="dlgCaixaForteCustodia" positionType="absolute"
                          modal="true" closable="true" resizable="false" dynamic="true" draggable="false" responsive="true"
                          showEffect="drop" hideEffect="drop" height="310" styleClass="dialogo"
                          style="border-top:4px solid #3C8DBC !important; min-width:95% !important;width:95% !important;max-width:95% !important;overflow:hidden !important;background-color:#EEE !important">

                    <f:facet name="header">
                        <img src="assets/img/icones_custodia_CAIXAFORTE.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.CaixaForte}" style="color:#3C8DBC; margin-left: 0px !important; font-weight:bold; font-size: 12pt; position: absolute; left: 70px; top:25px" />
                        <p:selectBooleanCheckbox id="chkCorporativo" itemLabel="  #{localemsgs.Corporativo}" value="#{custodia.custodiaCorporativo}">
                            <p:ajax update="msgs tabelaCxForte" listener="#{custodia.mostrarCustodiaCorporativo}" />
                        </p:selectBooleanCheckbox>
                    </f:facet>

                    <p:panel id="pnlFundoCustodia" style="display: inline; background-color: #EEE !important; overflow: auto !important">
                        <p:dataTable
                            id="tabelaCxForte"
                            value="#{custodia.cxForteLista}"
                            rowKey="#{listaCaixasForte.codCli}"
                            paginator="false"
                            paginatorTemplate="false"
                            lazy="true"
                            reflow="true"
                            var="listaCaixasForte"
                            scrollable="true"
                            styleClass="tabela"
                            selectionMode="single"
                            emptyMessage="#{localemsgs.SemRegistros}"

                            class="tabela DataGrid"
                            scrollWidth="100%"
                            style="font-size: 12px; background: white; padding:0px !important; margin:0px !important; min-height: 200px !important; max-width:100% !important; width:100% !important; background-color:#EEE !important; overflow: auto !important"
                            >
                            <p:ajax event="rowDblselect" listener="#{custodia.dblSelect}" />
                            <p:column headerText="#{localemsgs.Codigo}" class="text-center">
                                <h:outputText value="#{listaCaixasForte.codFil}" class="text-center">
                                    <f:convertNumber pattern="0000"/>
                                </h:outputText>
                            </p:column>
                            <p:column headerText="#{localemsgs.Filial}" class="text-center">
                                <h:outputText value="#{listaCaixasForte.filial}" class="text-center"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.NRed}" class="text-center">
                                <h:outputText value="#{listaCaixasForte.NRed}" class="text-center"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.SaldoReal}" class="text-center NegDestaque">
                                <h:outputText value="#{listaCaixasForte.saldoReal}" converter="conversormoeda" class="text-center NegDestaque"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.Saldo}" class="text-center NegDestaque">
                                <h:outputText value="#{listaCaixasForte.saldo}" converter="conversormoeda" class="text-center NegDestaque"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.QtdeRemessasCustodia}" class="text-center">
                                <h:outputText value="#{listaCaixasForte.remPendQtde}" converter="conversor0" class="text-center"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.ValorRemessasCustodia}" class="text-center NegDestaque">
                                <h:outputText value="#{listaCaixasForte.remPendValor}" converter="conversormoeda" class="text-center NegDestaque"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.QtdeRemessasPreparadas}" class="text-center">
                                <h:outputText value="#{listaCaixasForte.remPrepQtde}" converter="conversor0" class="text-center"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.ValorRemessasPreparadas}" class="text-center">
                                <h:outputText value="#{listaCaixasForte.remPrepValor}" converter="conversormoeda" class="text-center"/>
                            </p:column>



                            <p:column headerText="#{localemsgs.UltimaEntrada} - #{localemsgs.Data}" class="text-center">
                                <h:outputText value="#{listaCaixasForte.ultMovDataEntrada}" converter="conversorData" class="text-center"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.UltimaEntrada} - #{localemsgs.Hora}" class="text-center">
                                <h:outputText value="#{listaCaixasForte.ultMovHoraEntrada}" converter="conversorHora" class="text-center"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.UltimaEntrada} - #{localemsgs.Valor}" class="text-center">
                                <h:outputText value="#{listaCaixasForte.ultMovValorEntrada}" converter="conversormoeda" class="text-center"/>
                            </p:column>

                            <p:column headerText="#{localemsgs.UltimaSaida} - #{localemsgs.Data}" class="text-center">
                                <h:outputText value="#{listaCaixasForte.ultMovDataSaida}" converter="conversorData" class="text-center"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.UltimaSaida} - #{localemsgs.Hora}" class="text-center">
                                <h:outputText value="#{listaCaixasForte.ultMovHoraSaida}" converter="conversorHora" class="text-center"/>
                            </p:column>
                            <p:column headerText="#{localemsgs.UltimaSaida} - #{localemsgs.Valor}" class="text-center">
                                <h:outputText value="#{listaCaixasForte.ultMovValorSaida}" converter="conversormoeda" class="text-center"/>
                            </p:column>
                        </p:dataTable>
                    </p:panel>
                    <label style="background-color: lightyellow;position:absolute; bottom:10px; text-shadow:1px 1px #FFF; border:thin solid darkorange !important; color: darkorange !important; font-size:16pt; width:calc(100% - 30px); padding:6px !important; text-align:center;">#{localemsgs.SelecioneCaixaForte}</label>
                </p:dialog>
            </h:form>

            <!-- Exportação -->
            <h:form id="formExportacao" >
                <p:dialog widgetVar="dlgExportacao" header="#{localemsgs.Exportar}" positionType="absolute"
                          modal="true" closable="true" resizable="false" dynamic="true" draggable="false" responsive="true"
                          showEffect="drop" hideEffect="drop" height="310" styleClass="dialogo"
                          style="border-top:4px solid #3C8DBC !important; max-width:95% !important;overflow:hidden !important;background-color:#EEE !important">
                    <p:panel id="exportar" style="background-color: transparent; height: 100%" styleClass="panelMenu">
                        <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4"
                                     styleClass="ui-panelgrid-blank" style="width: 100%; text-align: center">
                            <h:panelGrid columns="1" style="width: 100%; text-align: center">
                                <p:commandLink oncomplete="PF('dlgExportarPessoa').show();">
                                    <f:actionListener binding="#{exportarMB.setTitulo(localemsgs.Pessoas)}"/>
                                    <f:actionListener binding="#{pessoa.Persistencias(login.pp, login.satellite)}"/>
                                    <p:graphicImage url="assets/img/icone_pessoas.png" width="120px" />
                                </p:commandLink>
                                <p:commandLink oncomplete="PF('dlgExportarPessoa').show();">
                                    <f:actionListener binding="#{exportarMB.setTitulo(localemsgs.Pessoas)}"/>
                                    <f:actionListener binding="#{pessoa.Persistencias(login.pp, login.satellite)}"/>
                                    <p:outputLabel value="#{localemsgs.Pessoas}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="width: 100%; text-align: center">
                                <p:commandLink oncomplete="PF('dlgExportarFuncion').show();">
                                    <f:actionListener binding="#{exportarMB.setTitulo(localemsgs.Funcion)}"/>
                                    <f:actionListener binding="#{funcionario.Persistencias(login.pp, login.satellite)}"/>
                                    <p:graphicImage url="assets/img/icone_funcionarios.png" width="120px" />
                                </p:commandLink>
                                <p:commandLink oncomplete="PF('dlgExportarFuncion').show();">
                                    <f:actionListener binding="#{exportarMB.setTitulo(localemsgs.Funcion)}"/>
                                    <f:actionListener binding="#{funcionario.Persistencias(login.pp, login.satellite)}"/>
                                    <p:outputLabel value="#{localemsgs.Funcionarios}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>
                        </p:panelGrid>

                        <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-2,ui-grid-col-4" styleClass="ui-panelgrid-blank"
                                     style="width: 100%; text-align: center">
                            <h:panelGrid columns="1" style="width: 100%; text-align: center">
                                <p:commandLink oncomplete="PF('dlgExportarPstServ').show();">
                                    <f:actionListener binding="#{exportarMB.setTitulo(localemsgs.PstServ)}"/>
                                    <f:actionListener binding="#{postoservico.Persistencia(login.pp)}"/>
                                    <p:graphicImage url="assets/img/icone_postosdeservico.png" width="120" />
                                </p:commandLink>
                                <p:commandLink oncomplete="PF('dlgExportarPstServ').show();">
                                    <f:actionListener binding="#{exportarMB.setTitulo(localemsgs.PstServ)}"/>
                                    <f:actionListener binding="#{postoservico.Persistencia(login.pp)}"/>
                                    <p:outputLabel value="#{localemsgs.PostoServicos}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="width: 100%; text-align: center">
                                <h:panelGrid columns="1" style="text-align: center">
                                    <p:commandLink oncomplete="PF('dlgExportarFiliais').show();">
                                        <f:actionListener binding="#{exportarMB.setTitulo(localemsgs.Filiais)}"/>
                                        <f:actionListener binding="#{filiaisMB.Persistencias(login.pp)}"/>
                                        <p:graphicImage url="assets/img/icone_filiais.png" width="120" />
                                    </p:commandLink>
                                    <p:commandLink oncomplete="PF('dlgExportarFiliais').show();">
                                        <f:actionListener binding="#{exportarMB.setTitulo(localemsgs.Filiais)}"/>
                                        <f:actionListener binding="#{filiaisMB.Persistencias(login.pp)}"/>
                                        <p:outputLabel value="#{localemsgs.Filiais}"
                                                       style="color:#022a48;"/>
                                    </p:commandLink>
                                </h:panelGrid>
                            </h:panelGrid>
                        </p:panelGrid>
                    </p:panel>
                </p:dialog>
            </h:form>

            <!-- EXPORTAR PESSOA -->
            <h:form id="exportarPessoa" class="form-inline">
                <p:dialog widgetVar="dlgExportarPessoa" positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                          showEffect="drop" hideEffect="drop" closeOnEscape="false" width="400" styleClass="dialogo"
                          style="border-top:4px solid #3C8DBC !important; max-width:95% !important;overflow:hidden !important;background-color:#EEE !important;">
                    <f:facet name="header">
                        <img src="assets/img/icone_pessoas.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.Exportar}" style="color:#022a48" />
                    </f:facet>
                    <p:dataTable id="tabela" value="#{pessoa.allPessoa}" paginator="true" rows="1" lazy="true"
                                 rowsPerPageTemplate="5,10,15, 20, 25"
                                 currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Pessoas}"
                                 paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                 var="listaPessoa" rowKey="#{listaPessoa.codigo}"
                                 resizableColumns="true" selectionMode="single" styleClass="tabela"
                                 selection="#{pessoa.pessoaSelecionada}" emptyMessage="#{localemsgs.SemRegistros}"
                                 scrollable="true" scrollWidth="100%"
                                 style="font-size: 12px; background: white; display: none">
                        <p:column headerText="#{localemsgs.Codigo}" style="width: 50px" exportable="#{pessoa.eCodigo}">
                            <h:outputText value="#{listaPessoa.codigo}">
                                <f:convertNumber pattern="0000"/>
                            </h:outputText>
                        </p:column>
                        <p:column headerText="#{localemsgs.Nome}" style="width: 300px" exportable="#{pessoa.eNome}">
                            <h:outputText value="#{listaPessoa.nome}"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.Email}" style="width: 300px" exportable="#{pessoa.eEmail}">
                            <h:outputText value="#{listaPessoa.email}"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.RG}" style="width: 95px" exportable="#{pessoa.eRG}">
                            <h:outputText value="#{listaPessoa.RG}"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.RGOrg}" style="width: 95px" exportable="#{pessoa.eOrgEmis}">
                            <h:outputText value="#{listaPessoa.RGOrgEmis}"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.CPF}" style="width: 95px" exportable="#{pessoa.eCPF}">
                            <h:outputText value="#{listaPessoa.CPF}" converter="conversorCPF"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.Fone1}" style="width: 100px" exportable="#{pessoa.eFone1}">
                            <h:outputText value="#{listaPessoa.fone1}" converter="conversorFone"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.Fone2}" style="width: 100px" exportable="#{pessoa.eFone2}">
                            <h:outputText value="#{listaPessoa.fone2}" converter="conversorFone"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.Endereco}" style="width: 200px" exportable="#{pessoa.eEnde}">
                            <h:outputText value="#{listaPessoa.endereco}"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.Bairro}" style="width: 115px" exportable="#{pessoa.eBairro}">
                            <h:outputText value="#{listaPessoa.bairro}"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.Cidade}" style="width: 100px" exportable="#{pessoa.eCidade}">
                            <h:outputText value="#{listaPessoa.cidade}"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.UF}" style="width: 30px" exportable="#{pessoa.eUF}">
                            <h:outputText value="#{listaPessoa.UF}"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.CEP}" style="width: 80px" exportable="#{pessoa.eCEP}">
                            <h:outputText value="#{listaPessoa.CEP}" converter="conversorCEP"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.Situacao}" style="width: 60px" exportable="#{pessoa.eSituacao}">
                            <h:outputText value="#{listaPessoa.situacao}"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.Obs}" style="width: 90px" exportable="#{pessoa.eObs}">
                            <h:outputText value="#{listaPessoa.obs}"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.Matr}" style="width: 75px" exportable="#{pessoa.eMatr}">
                            <h:outputText value="#{listaPessoa.matr}">
                                <f:convertNumber pattern="000000"/>
                            </h:outputText>
                        </p:column>
                        <p:column headerText="#{localemsgs.Funcao}" style="width: 80px" exportable="#{pessoa.eFuncao}">
                            <h:outputText value="#{listaPessoa.funcao}"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.Sexo}" style="width: 84px" exportable="#{pessoa.eSexo}">
                            <h:outputText value="#{listaPessoa.sexo}"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.Altura}" style="width: 84px" exportable="#{pessoa.eAltura}">
                            <h:outputText value="#{listaPessoa.altura}">
                                <f:convertNumber pattern="000"/>
                            </h:outputText>
                        </p:column>
                        <p:column headerText="#{localemsgs.Peso}" style="width: 84px" exportable="#{pessoa.ePeso}">
                            <h:outputText value="#{listaPessoa.peso}">
                                <f:convertNumber pattern="000"/>
                            </h:outputText>
                        </p:column>
                        <p:column headerText="#{localemsgs.Operador}" style="width: 84px" exportable="#{pessoa.eOperador}">
                            <h:outputText value="#{listaPessoa.operador}"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.Dt_Alter}" style="width: 84px" exportable="#{pessoa.eDtAlter}">
                            <h:outputText value="#{listaPessoa.dt_Alter}" converter="conversorData"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.Hr_Alter}" style="width: 58px" exportable="#{pessoa.eHrAlter}">
                            <h:outputText value="#{listaPessoa.hr_Alter}"/>
                        </p:column>
                    </p:dataTable>
                    <h:outputText value="#{localemsgs.CamposExportacao}:"/>
                    <p:separator />
                    <div class="ui-grid-row" style="padding-bottom: 3px;">
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="codigo" value="#{pessoa.eCodigo}">
                                <p:ajax update="labelCodigo"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelCodigo" value="#{localemsgs.Codigo}" style="#{pessoa.eCodigo eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="nome" value="#{pessoa.eNome}">
                                <p:ajax update="labelNome"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelNome" value="#{localemsgs.Nome}" style="#{pessoa.eNome eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="email" value="#{pessoa.eEmail}">
                                <p:ajax update="labelEmail"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelEmail" value="#{localemsgs.Email}" style="#{pessoa.eEmail eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="cpf" value="#{pessoa.eCPF}">
                                <p:ajax update="labelCPF"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelCPF" value="#{localemsgs.CPF}" style="#{pessoa.eCPF eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                    </div>
                    <div class="ui-grid-row" style="padding-bottom: 3px;">
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="rg" value="#{pessoa.eRG}">
                                <p:ajax update="labelRG"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelRG" value="#{localemsgs.RG}" style="#{pessoa.eRG ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="orgemis" value="#{pessoa.eOrgEmis}">
                                <p:ajax update="labelOrgEmis"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelOrgEmis" value="#{localemsgs.RGOrg}" style="#{pessoa.eOrgEmis eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="fon1" value="#{pessoa.eFone1}">
                                <p:ajax update="labelFon1"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelFon1" value="#{localemsgs.Fone1}" style="#{pessoa.eFone1 eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="fon2" value="#{pessoa.eFone2}">
                                <p:ajax update="labelFon2"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelFon2" value="#{localemsgs.Fone2}" style="#{pessoa.eFone2 eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                    </div>
                    <div class="ui-grid-row" style="padding-bottom: 3px;">
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="ende" value="#{pessoa.eEnde}">
                                <p:ajax update="labelEnde"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelEnde" value="#{localemsgs.Ende}" style="#{pessoa.eEnde eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="bairro" value="#{pessoa.eBairro}">
                                <p:ajax event="change" update="labelBairro"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelBairro"  value="#{localemsgs.Bairro}" style="#{pessoa.eBairro eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="cidade" value="#{pessoa.eCidade}">
                                <p:ajax update="labelCidade"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelCidade" value="#{localemsgs.Cidade}" style="#{pessoa.eCidade eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="uf" value="#{pessoa.eUF}">
                                <p:ajax update="labelUF"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelUF" value="#{localemsgs.UF}" style="#{pessoa.eUF eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                    </div>
                    <div class="ui-grid-row" style="padding-bottom: 3px;">
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="cep" value="#{pessoa.eCEP}">
                                <p:ajax update="labelCEP"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelCEP" value="#{localemsgs.CEP}" style="#{pessoa.eCEP eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="sit" value="#{pessoa.eSituacao}">
                                <p:ajax update="labelSit"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelSit" value="#{localemsgs.Situacao}" style="#{pessoa.eSituacao eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="operador" value="#{pessoa.eOperador}">
                                <p:ajax update="labelOperador"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelOperador" value="#{localemsgs.Operador}" style="#{pessoa.eOperador eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="matr" value="#{pessoa.eMatr}">
                                <p:ajax update="labelMatr"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelMatr" value="#{localemsgs.Matr}" style="#{pessoa.eMatr eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                    </div>
                    <div class="ui-grid-row" style="padding-bottom: 3px;">
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="func" value="#{pessoa.eFuncao}">
                                <p:ajax update="labelFunc"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelFunc" value="#{localemsgs.Funcao}" style="#{pessoa.eFuncao eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="sex" value="#{pessoa.eSexo}">
                                <p:ajax update="labelSex"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelSex" value="#{localemsgs.Sexo}" style="#{pessoa.eSexo eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="altura" value="#{pessoa.eAltura}">
                                <p:ajax update="labelAlt"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelAlt" value="#{localemsgs.Altura}" style="#{pessoa.eAltura eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="peso" value="#{pessoa.ePeso}">
                                <p:ajax update="labelPeso"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelPeso" value="#{localemsgs.Peso}" style="#{pessoa.ePeso eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                    </div>
                    <div class="ui-grid-row" style="padding-bottom: 3px;">

                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="dtalter" value="#{pessoa.eDtAlter}">
                                <p:ajax update="labelDtAlter"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelDtAlter" value="#{localemsgs.Dt_Alter}" style="#{pessoa.eDtAlter eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="hralter" value="#{pessoa.eHrAlter}">
                                <p:ajax update="labelHrAlter"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelHrAlter" value="#{localemsgs.Hr_Alter}" style="#{pessoa.eHrAlter eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="obs" value="#{pessoa.eObs}">
                                <p:ajax update="labelObs"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelObs" value="#{localemsgs.Obs}" style="#{pessoa.eObs eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                    </div>
                    <p:separator />

                    <p:panelGrid columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                 layout="grid" styleClass="ui-panelgrid-blank">
                        <p:panel style="text-align: center">
                            <p:outputLabel for="pdf" value="#{localemsgs.pdf}:" style="font-weight: bold"/>
                            <h:commandLink id="pdf" actionListener="#{pessoa.AtualizaTabelaMenu}">
                                <p:graphicImage url="assets/img/icone_pdf.png"/>
                                <p:dataExporter target="exportarPessoa:tabela" type="pdf" fileName="#{localemsgs.Pessoas}"
                                                preProcessor="#{exportarMB.PdfPreProcessor}" encoding="iso-8859-1"/>
                            </h:commandLink>
                        </p:panel>

                        <p:panel style="text-align: center">
                            <p:outputLabel for="xlsx" value="#{localemsgs.xls}:" style="font-weight: bold"/>
                            <h:commandLink id="xlsx" actionListener="#{pessoa.AtualizaTabelaMenu}">
                                <p:graphicImage url="assets/img/icone_xls.png"/>
                                <p:dataExporter target="exportarPessoa:tabela" type="xlsx" fileName="#{localemsgs.Pessoas}"/>
                            </h:commandLink>
                        </p:panel>
                    </p:panelGrid>
                </p:dialog>
            </h:form>

            <!-- EXPORTAR FUNCIONARIOS -->
            <h:form id="exportarFuncion" class="form-inline">
                <p:dialog widgetVar="dlgExportarFuncion" positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                          showEffect="drop" hideEffect="drop" closeOnEscape="false" width="400" styleClass="dialogo"
                          style="border-top:4px solid #3C8DBC !important; max-width:95% !important;overflow:hidden !important;background-color:#EEE !important;">
                    <f:facet name="header">
                        <img src="assets/img/icone_satmob_funcionarios.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.Exportar}" style="color:#022a48" />
                    </f:facet>
                    <p:dataTable id="tabela" value="#{funcionario.allFuncionMenu}" paginator="true" rows="1" lazy="true"
                                 rowsPerPageTemplate="5,10,15, 20, 25"
                                 currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Funcionarios}"
                                 paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                 var="lista" rowKey="#{lista.funcion.matr}"
                                 resizableColumns="true" selectionMode="single" styleClass="tabela"
                                 selection="#{funcionario.selecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                 scrollable="true" scrollWidth="100%"
                                 style="font-size: 12px; background: white; display: none">
                        <p:column headerText="#{localemsgs.CodFil}" style="width: 49px" class="celula-right" exportable="#{funcionario.eFilial}">
                            <h:outputText value="#{lista.funcion.codFil}" title="#{lista.funcion.codFil}">
                                <f:convertNumber pattern="0000"/>
                            </h:outputText>
                        </p:column>
                        <p:column headerText="#{localemsgs.Matr}" style="width: 63px" exportable="#{funcionario.eMatr}">
                            <h:outputText value="#{lista.funcion.matr}">
                                <f:convertNumber pattern="00000000"/>
                            </h:outputText>
                        </p:column>
                        <p:column headerText="#{localemsgs.Nome_Guer}" style="width: 147px" exportable="#{funcionario.eNomeGuer}">
                            <h:outputText value="#{lista.funcion.nome_Guer}" title="#{lista.funcion.nome_Guer}"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.Nome}" style="width: 350px" exportable="#{funcionario.eNome}">
                            <h:outputText value="#{lista.funcion.nome}" title="#{lista.funcion.nome}"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.CPF}" style="width: 96px" exportable="#{funcionario.eCPF}">
                            <h:outputText value="#{lista.funcion.CPF}" title="#{lista.funcion.CPF}" converter="conversorCPF"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.Email}" style="width: 350px" exportable="#{funcionario.eEmail}">
                            <h:outputText value="#{lista.funcion.email}" title="#{lista.funcion.email}"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.RG}" style="width: 134px" exportable="#{funcionario.eRG}">
                            <h:outputText value="#{lista.funcion.RG}" title="#{lista.funcion.RG}"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.RGOrg}" style="width: 134px" exportable="#{funcionario.eOrgEmis}">
                            <h:outputText value="#{lista.funcion.orgEmis}" title="#{lista.funcion.orgEmis}"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.Dt_Nasc}" style="width: 73px" exportable="#{funcionario.eDtNasc}">
                            <h:outputText value="#{lista.funcion.dt_Nasc}" title="#{lista.funcion.dt_Nasc}" converter="conversorData"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.Secao}" style="width: 103px" exportable="#{funcionario.eCodPosto}">
                            <h:outputText value="#{lista.funcion.secao}" title="#{lista.funcion.secao}"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.Posto}" style="width: 411px" exportable="#{funcionario.ePosto}">
                            <h:outputText value="#{lista.pstserv.local}" title="#{lista.pstserv.local}"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.Situacao}" style="width: 15px" exportable="#{funcionario.eSituacao}">
                            <h:outputText value="#{lista.funcion.situacao}" title="#{lista.funcion.situacao}"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.Dt_Situac}" style="width: 73px" exportable="#{funcionario.eDtSituacao}">
                            <h:outputText value="#{lista.funcion.dt_Situac}" title="#{lista.funcion.dt_Situac}" converter="conversorData"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.Dt_Admis}" style="width: 73px" exportable="#{funcionario.eDtAdmis}">
                            <h:outputText value="#{lista.funcion.dt_Admis}" title="#{lista.funcion.dt_Admis}" converter="conversorData"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.InterfExt}" style="width: 73px" exportable="#{funcionario.eInterfExt}">
                            <h:outputText value="#{lista.pstserv.interfExt}" title="#{lista.pstserv.interfExt}"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.CodPessoaWeb}" style="width: 95px" class="celula-right" exportable="#{funcionario.eCodPessoaWeb}">
                            <h:outputText value="#{lista.funcion.codPessoaWeb}" title="#{lista.funcion.codPessoaWeb}"
                                          converter="conversor0"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.Operador}" style="width: 95px" exportable="#{funcionario.eOperador}">
                            <h:outputText value="#{lista.funcion.operador}" title="#{lista.funcion.operador}"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.Dt_Alter}"  style="width: 73px" exportable="#{funcionario.eDtAlter}">
                            <h:outputText value="#{lista.funcion.dt_Alter}" title="#{lista.funcion.dt_Alter}" converter="conversorData"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.Hr_Alter}" style="width: 59px" class="celula-right" exportable="#{funcionario.eHrAlter}">
                            <h:outputText value="#{lista.funcion.hr_Alter}" title="#{lista.funcion.hr_Alter}"/>
                        </p:column>
                    </p:dataTable>
                    <h:outputText value="#{localemsgs.CamposExportacao}:"/>
                    <p:separator />
                    <div class="ui-grid-row" style="padding-bottom: 3px;">
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="filial" value="#{funcionario.eFilial}">
                                <p:ajax update="labelFilial"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelFilial" value="#{localemsgs.Filial}" style="#{funcionario.eFilial eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="matr" value="#{funcionario.eMatr}">
                                <p:ajax update="labelMatr"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelMatr" value="#{localemsgs.Matr}" style="#{funcionario.eMatr eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="nome" value="#{funcionario.eNome}">
                                <p:ajax update="labelNome"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelNome" value="#{localemsgs.Nome}" style="#{funcionario.eNome eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="email" value="#{funcionario.eEmail}">
                                <p:ajax update="labelEmail"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelEmail" value="#{localemsgs.Email}" style="#{funcionario.eEmail eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                    </div>
                    <div class="ui-grid-row" style="padding-bottom: 3px;">
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="cpf" value="#{funcionario.eCPF}">
                                <p:ajax update="labelCPF"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelCPF" value="#{localemsgs.CPF}" style="#{funcionario.eCPF eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="rg" value="#{funcionario.eRG}">
                                <p:ajax update="labelRG"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelRG" value="#{localemsgs.RG}" style="#{funcionario.eRG ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="orgemis" value="#{funcionario.eOrgEmis}">
                                <p:ajax update="labelOrgEmis"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelOrgEmis" value="#{localemsgs.RGOrg}" style="#{funcionario.eOrgEmis eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="NomeGuer" value="#{funcionario.eNomeGuer}">
                                <p:ajax update="eNomeGuer"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="eNomeGuer" value="#{localemsgs.Nome_Guer}" style="#{funcionario.eNomeGuer eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                    </div>
                    <div class="ui-grid-row" style="padding-bottom: 3px;">
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="nasc" value="#{funcionario.eDtNasc}">
                                <p:ajax update="labelNasc"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelNasc" value="#{localemsgs.Dt_Nasc}" style="#{funcionario.eDtNasc eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="sit" value="#{funcionario.eSituacao}">
                                <p:ajax update="labelSit"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelSit" value="#{localemsgs.Situacao}" style="#{funcionario.eSituacao eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="eDtSituacao" value="#{funcionario.eDtSituacao}">
                                <p:ajax update="labelSituacao"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelSituacao" value="#{localemsgs.Dt_Situac}" style="#{funcionario.eDtSituacao eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="eDtAdmis" value="#{funcionario.eDtAdmis}">
                                <p:ajax update="labelDtAdmis"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelDtAdmis" value="#{localemsgs.Dt_Admis}" style="#{funcionario.eDtAdmis eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                    </div>
                    <div class="ui-grid-row" style="padding-bottom: 3px;">
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="posto" value="#{funcionario.ePosto}">
                                <p:ajax update="labelPosto"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelPosto"  value="#{localemsgs.Posto}" style="#{funcionario.ePosto eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="CodPosto" value="#{funcionario.eCodPosto}">
                                <p:ajax update="eCodPosto"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="eCodPosto"  value="#{localemsgs.Secao}" style="#{funcionario.eCodPosto eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="CodPessoaWeb" value="#{funcionario.eCodPessoaWeb}">
                                <p:ajax update="eCodPessoaWeb"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="eCodPessoaWeb"  value="#{localemsgs.CodPessoa}" style="#{funcionario.eCodPessoaWeb eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="InterfExt" value="#{funcionario.eInterfExt}">
                                <p:ajax update="eInterfExt"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="eInterfExt" value="#{localemsgs.InterfExt}" style="#{funcionario.eInterfExt eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                    </div>
                    <div class="ui-grid-row" style="padding-bottom: 3px;">
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="hralter" value="#{funcionario.eHrAlter}">
                                <p:ajax update="labelHrAlter"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelHrAlter" value="#{localemsgs.Hr_Alter}" style="#{funcionario.eHrAlter eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="dtalter" value="#{funcionario.eDtAlter}">
                                <p:ajax update="labelDtAlter"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelDtAlter" value="#{localemsgs.Dt_Alter}" style="#{funcionario.eDtAlter eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="operador" value="#{funcionario.eOperador}">
                                <p:ajax update="labelOperador"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelOperador" value="#{localemsgs.Operador}" style="#{funcionario.eOperador eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                    </div>
                    <p:separator />
                    <p:panelGrid columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                 layout="grid" styleClass="ui-panelgrid-blank">
                        <p:panel style="text-align: center">
                            <p:outputLabel for="pdf" value="#{localemsgs.pdf}:" style="font-weight: bold"/>
                            <h:commandLink id="pdf" actionListener="#{funcionario.AtualizaTabelaMenu}">
                                <p:graphicImage url="assets/img/icone_pdf.png"/>
                                <p:dataExporter target="exportarFuncion:tabela" type="pdf" fileName="#{localemsgs.Funcionarios}"
                                                preProcessor="#{exportarMB.PdfPreProcessor}" encoding="iso-8859-1"/>
                            </h:commandLink>
                        </p:panel>

                        <p:panel style="text-align: center">
                            <p:outputLabel for="xlsx" value="#{localemsgs.xls}:" style="font-weight: bold"/>
                            <h:commandLink id="xlsx" actionListener="#{funcionario.AtualizaTabelaMenu}">
                                <p:graphicImage url="assets/img/icone_xls.png"/>
                                <p:dataExporter target="exportarFuncion:tabela" type="xlsx" fileName="#{localemsgs.Funcionarios}"/>
                            </h:commandLink>
                        </p:panel>
                    </p:panelGrid>
                </p:dialog>
            </h:form>

            <!-- EXPORTAR POSTOS -->
            <h:form id="exportarPstServ" class="form-inline">
                <p:dialog widgetVar="dlgExportarPstServ" positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                          showEffect="drop" hideEffect="drop" closeOnEscape="false" width="400" styleClass="dialogo"
                          style="border-top:4px solid #3C8DBC !important; max-width:95% !important;overflow:hidden !important;background-color:#EEE !important;">
                    <f:facet name="header">
                        <img src="assets/img/icone_satmob_postosdeservico.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.Exportar}" style="color:#022a48" />
                    </f:facet>
                    <p:dataTable id="tabela" value="#{postoservico.allPostosMenu}" paginator="true" rows="1" lazy="true"
                                 rowsPerPageTemplate="5,10,15, 20, 25"
                                 currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.PstServ}"
                                 paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                 var="lista"
                                 resizableColumns="true" selectionMode="single" styleClass="tabela"
                                 selection="#{postoservico.selecionado}" emptyMessage="#{localemsgs.SemRegistros}"
                                 scrollable="true" scrollWidth="100%"
                                 style="font-size: 12px; background: white; display: none">
                        <p:column headerText="#{localemsgs.CodFil}" style="width: 55px" exportable="#{postoservico.eFilial}">
                            <h:outputText value="#{lista.codFil}" title="#{lista.codFil}">
                                <f:convertNumber pattern="0000" />
                            </h:outputText>
                        </p:column>
                        <p:column headerText="#{localemsgs.Secao}" style="width: 130px" exportable="#{postoservico.eCodPosto}">
                            <h:outputText value="#{lista.secao}" title="#{lista.secao}" />
                        </p:column>
                        <p:column headerText="#{localemsgs.Local}" style="width: 360px" exportable="#{postoservico.ePosto}">
                            <h:outputText value="#{lista.local}" title="#{lista.local}" />
                        </p:column>
                        <p:column headerText="#{localemsgs.TipoPosto}" style="width: 45px" exportable="#{postoservico.eTipo}">
                            <h:outputText value="#{lista.tipoPosto}" title="#{lista.tipoPosto}" />
                        </p:column>
                        <p:column headerText="#{localemsgs.TipoPostoDesc}" style="width: 265px" exportable="#{postoservico.eDescTipo}">
                            <h:outputText value="#{lista.tipoPostoDesc}" title="#{lista.tipoPostoDesc}"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.Situacao}" style="width: 60px" exportable="#{postoservico.eSituacao}">
                            <h:outputText value="#{lista.situacao}" title="#{lista.situacao}" />
                        </p:column>
                        <p:column headerText="#{localemsgs.Dt_Situacao}" style="width: 90px" exportable="#{postoservico.eDtSituacao}">
                            <h:outputText value="#{lista.dt_Situacao}"
                                          converter="conversorData" />
                        </p:column>
                        <p:column headerText="#{localemsgs.InterfExt}" style="width: 120px" exportable="#{postoservico.eInterfExt}">
                            <h:outputText value="#{lista.interfExt}" title="#{lista.interfExt}" />
                        </p:column>
                        <p:column headerText="#{localemsgs.Operador}" style="width: 110px" exportable="#{postoservico.eOperador}">
                            <h:outputText value="#{lista.operador}" title="#{lista.operador}" />
                        </p:column>
                        <p:column headerText="#{localemsgs.Dt_Alter}" style="width: 90px" exportable="#{postoservico.eDtAlter}">
                            <h:outputText value="#{lista.dt_Alter}" converter="conversorData"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.Hr_Alter}" style="width: 60px" exportable="#{postoservico.eHrAlter}">
                            <h:outputText value="#{lista.hr_Alter}" title="#{lista.hr_Alter}" />
                        </p:column>
                    </p:dataTable>
                    <h:outputText value="#{localemsgs.CamposExportacao}:"/>
                    <p:separator />
                    <div class="ui-grid-row" style="padding-bottom: 3px;">
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="filial" value="#{postoservico.eFilial}">
                                <p:ajax update="labelFilial"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelFilial" value="#{localemsgs.Filial}" style="#{postoservico.eFilial eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="secao" value="#{postoservico.eCodPosto}">
                                <p:ajax update="labelSecao"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelSecao" value="#{localemsgs.Secao}" style="#{postoservico.eCodPosto eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="local" value="#{postoservico.ePosto}">
                                <p:ajax update="labelLocal"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelLocal" value="#{localemsgs.Posto}" style="#{postoservico.ePosto eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="eTipo" value="#{postoservico.eTipo}">
                                <p:ajax update="labelTipo"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelTipo" value="#{localemsgs.TipoPosto}" style="#{postoservico.eTipo eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                    </div>
                    <div class="ui-grid-row" style="padding-bottom: 3px;">
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="eDescTipo" value="#{postoservico.eDescTipo}">
                                <p:ajax update="labelTipoDesc"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelTipoDesc" value="#{localemsgs.TipoPostoDesc}" style="#{postoservico.eDescTipo eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="sit" value="#{postoservico.eSituacao}">
                                <p:ajax update="labelSit"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelSit" value="#{localemsgs.Situacao}" style="#{postoservico.eSituacao eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="eDtSituacao" value="#{postoservico.eDtSituacao}">
                                <p:ajax update="labelSituacao"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelSituacao" value="#{localemsgs.Dt_Situac}" style="#{postoservico.eDtSituacao eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="InterfExt" value="#{postoservico.eInterfExt}">
                                <p:ajax update="eInterfExt"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="eInterfExt" value="#{localemsgs.InterfExt}" style="#{postoservico.eInterfExt eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                    </div>
                    <div class="ui-grid-row" style="padding-bottom: 3px;">
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="hralter" value="#{postoservico.eHrAlter}">
                                <p:ajax update="labelHrAlter"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelHrAlter" value="#{localemsgs.Hr_Alter}" style="#{postoservico.eHrAlter eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="dtalter" value="#{postoservico.eDtAlter}">
                                <p:ajax update="labelDtAlter"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelDtAlter" value="#{localemsgs.Dt_Alter}" style="#{postoservico.eDtAlter eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="operador" value="#{postoservico.eOperador}">
                                <p:ajax update="labelOperador"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelOperador" value="#{localemsgs.Operador}" style="#{postoservico.eOperador eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                    </div>
                    <p:separator />
                    <p:panelGrid columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                 layout="grid" styleClass="ui-panelgrid-blank">
                        <p:panel style="text-align: center">
                            <p:outputLabel for="pdf" value="#{localemsgs.pdf}:" style="font-weight: bold"/>
                            <h:commandLink id="pdf" actionListener="#{postoservico.AtualizaTabelaMenu}">
                                <p:graphicImage url="assets/img/icone_pdf.png"/>
                                <p:dataExporter target="exportarPstServ:tabela" type="pdf" fileName="#{localemsgs.Postos}"
                                                preProcessor="#{exportarMB.PdfPreProcessor}" encoding="iso-8859-1"/>
                            </h:commandLink>
                        </p:panel>

                        <p:panel style="text-align: center">
                            <p:outputLabel for="xlsx" value="#{localemsgs.xls}:" style="font-weight: bold"/>
                            <h:commandLink id="xlsx" actionListener="#{postoservico.AtualizaTabelaMenu}">
                                <p:graphicImage url="assets/img/icone_xls.png"/>
                                <p:dataExporter target="exportarPstServ:tabela" type="xlsx" fileName="#{localemsgs.Postos}"/>
                            </h:commandLink>
                        </p:panel>
                    </p:panelGrid>
                </p:dialog>
            </h:form>

            <!-- EXPORTAR FILIAIS -->
            <h:form id="exportarFiliais" class="form-inline">
                <p:dialog widgetVar="dlgExportarFiliais" positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                          showEffect="drop" hideEffect="drop" closeOnEscape="false" width="400" styleClass="dialogo"
                          style="border-top:4px solid #3C8DBC !important; max-width:95% !important;overflow:hidden !important;background-color:#EEE !important;">
                    <f:facet name="header">
                        <img src="assets/img/icone_filiais.png" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.Exportar}" style="color:#022a48" />
                    </f:facet>
                    <p:dataTable id="tabela" value="#{filiaisMB.allFiliaisMenu}" paginator="true" rows="1" lazy="true"
                                 rowsPerPageTemplate="5,10,15, 20, 25"
                                 currentPageReportTemplate="#{localemsgs.Mostrando} {startRecord} - {endRecord} #{localemsgs.De} {totalRecords} #{localemsgs.Filiais}"
                                 paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {RowsPerPageDropdown} {NextPageLink} {LastPageLink}"
                                 var="listaFiliais" rowKey="#{listaFiliais.codFil}"
                                 resizableColumns="true" selectionMode="single" styleClass="tabela"
                                 selection="#{filiaisMB.filialSelecionada}" emptyMessage="#{localemsgs.SemRegistros}"
                                 scrollable="true" scrollWidth="100%"
                                 style="font-size: 12px; background: white; display: none">
                        <p:column headerText="#{localemsgs.CodFil}" style="width: 35px" exportable="#{filiaisMB.eFilial}">
                            <h:outputText value="#{listaFiliais.codFil}">
                                <f:convertNumber pattern="0000"/>
                            </h:outputText>
                        </p:column>

                        <p:column headerText="#{localemsgs.Descricao}" style="width: 239px" exportable="#{filiaisMB.eDesc}">
                            <h:outputText value="#{listaFiliais.descricao}"/>
                        </p:column>

                        <p:column headerText="#{localemsgs.RazaoSocial}" style="width: 470px" exportable="#{filiaisMB.eRazao}">
                            <h:outputText value="#{listaFiliais.razaoSocial}"/>
                        </p:column>

                        <p:column headerText="#{localemsgs.Operador}" style="width: 122px" exportable="#{filiaisMB.eOperador}">
                            <h:outputText value="#{listaFiliais.operador}"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.Dt_Alter}" style="width: 84px" exportable="#{filiaisMB.eDtAlter}">
                            <h:outputText value="#{listaFiliais.dt_Alter}" title="#{listaFiliais.dt_Alter}" converter="conversorData"/>
                        </p:column>
                        <p:column headerText="#{localemsgs.Hr_Alter}" style="width: 49px" exportable="#{filiaisMB.eHrAlter}">
                            <h:outputText value="#{listaFiliais.hr_Alter}"/>
                        </p:column>
                    </p:dataTable>
                    <h:outputText value="#{localemsgs.CamposExportacao}:"/>
                    <p:separator />
                    <div class="ui-grid-row" style="padding-bottom: 3px;">
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="filial" value="#{filiaisMB.eFilial}">
                                <p:ajax update="labelFilial"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelFilial" value="#{localemsgs.Filial}" style="#{filiaisMB.eFilial eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="desc" value="#{filiaisMB.eDesc}">
                                <p:ajax update="labelDesc"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelDesc" value="#{localemsgs.Descricao}" style="#{filiaisMB.eDesc eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="razao" value="#{filiaisMB.eRazao}">
                                <p:ajax update="labelRazao"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelRazao" value="#{localemsgs.RazaoSocial}" style="#{filiaisMB.eRazao eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="operador" value="#{filiaisMB.eOperador}">
                                <p:ajax update="labelOperador"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelOperador" value="#{localemsgs.Operador}" style="#{filiaisMB.eOperador eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                    </div>
                    <div class="ui-grid-row" style="padding-bottom: 3px;">
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="dtalter" value="#{filiaisMB.eDtAlter}">
                                <p:ajax update="labelDtAlter"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelDtAlter" value="#{localemsgs.Dt_Alter}" style="#{filiaisMB.eDtAlter eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                        <div style="width: 5%; float: left;">
                            <p:selectBooleanCheckbox id="hralter" value="#{filiaisMB.eHrAlter}">
                                <p:ajax update="labelHrAlter"/>
                            </p:selectBooleanCheckbox>
                        </div>
                        <div style="width: 20%; float: left;">
                            <p:outputLabel id="labelHrAlter" value="#{localemsgs.Hr_Alter}" style="#{filiaisMB.eHrAlter eq true ? 'font-weight: bold' : 'font-weight: normal'}"/>
                        </div>
                    </div>
                    <p:separator />
                    <p:panelGrid columns="2" columnClasses="ui-grid-col-6,ui-grid-col-6"
                                 layout="grid" styleClass="ui-panelgrid-blank">
                        <p:panel style="text-align: center">
                            <p:outputLabel for="pdf" value="#{localemsgs.pdf}:" style="font-weight: bold"/>
                            <h:commandLink id="pdf" actionListener="#{filiaisMB.AtualizaTabelaMenu}">
                                <p:graphicImage url="assets/img/icone_pdf.png"/>
                                <p:dataExporter target="exportarFiliais:tabela" type="pdf" fileName="#{localemsgs.Filiais}"
                                                preProcessor="#{exportarMB.PdfPreProcessor}" encoding="iso-8859-1"/>
                            </h:commandLink>
                        </p:panel>

                        <p:panel style="text-align: center">
                            <p:outputLabel for="xlsx" value="#{localemsgs.xls}:" style="font-weight: bold"/>
                            <h:commandLink id="xlsx" actionListener="#{filiaisMB.AtualizaTabelaMenu}">
                                <p:graphicImage url="assets/img/icone_xls.png"/>
                                <p:dataExporter target="exportarFiliais:tabela" type="xlsx" fileName="#{localemsgs.Filiais}"/>
                            </h:commandLink>
                        </p:panel>
                    </p:panelGrid>
                </p:dialog>
            </h:form>

            <!--IMPORTAR-->
            <h:form id="formImportar">
                <p:dialog header="#{localemsgs.Importador}" widgetVar="dlgImportar" positionType="absolute"
                          modal="true" closable="true" resizable="false" dynamic="true"  draggable="false"
                          height="310"  showEffect="drop" hideEffect="drop" responsive="true" styleClass="dialogo"
                          style="border-top:4px solid #3C8DBC !important; max-width:95% !important;overflow:hidden !important;background-color:#EEE !important">
                    <p:panel id="cadastro" style="background-color: transparent" styleClass="panelMenu">
                        <p:panelGrid columns="3" columnClasses="ui-grid-col-4,ui-grid-col-4,ui-grid-col-4"
                                     styleClass="ui-panelgrid-blank" style="width: 100%; text-align: center;">
                            <h:panelGrid columns="1" style="width: 100%; text-align: center;">
                                <p:commandLink oncomplete="PF('dlgArqUp').show();" action="#{importarMB.LeParametro}"
                                               update="msgs" actionListener="#{importarMB.setParametro(1)}" >
                                    <p:graphicImage url="assets/img/icone_pessoas.png" width="120px" />
                                </p:commandLink>
                                <p:commandLink oncomplete="PF('dlgArqUp').show();"
                                               update="msgs" actionListener="#{importarMB.LeParametro}" >
                                    <f:actionListener binding="#{importarMB.setParametro(1)}"/>
                                    <p:outputLabel value="#{localemsgs.Pessoas}"
                                                   style="color:#022a48;"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="width: 100%; text-align: center;">
                                <p:commandLink oncomplete="PF('dlgArqUp').show();"
                                               update="msgs" action="#{importarMB.LeParametro}">
                                    <p:graphicImage url="assets/img/icone_funcionarios.png" width="120px" />
                                    <f:actionListener binding="#{importarMB.setParametro(2)}"/>
                                </p:commandLink>
                                <p:commandLink oncomplete="PF('dlgArqUp').show();"
                                               update="msgs" actionListener="#{importarMB.LeParametro}" >
                                    <p:outputLabel value="#{localemsgs.Funcionarios}"
                                                   style="color:#022a48;"/>
                                    <f:actionListener binding="#{importarMB.setParametro(2)}"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="width: 100%; text-align: center;">
                                <p:commandLink oncomplete="PF('dlgArqUp').show();"
                                               update="msgs" action="#{importarMB.LeParametro}">
                                    <p:graphicImage url="assets/img/icone_clientes.png" width="120px" />
                                    <f:actionListener binding="#{importarMB.setParametro(3)}"/>
                                </p:commandLink>
                                <p:commandLink oncomplete="PF('dlgArqUp').show();"
                                               update="msgs" action="#{importarMB.LeParametro}">
                                    <p:outputLabel value="#{localemsgs.Clientes}"
                                                   style="color:#022a48;"/>
                                    <f:actionListener binding="#{importarMB.setParametro(3)}"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="width: 100%; text-align: center;" >
                                <p:commandLink oncomplete="PF('dlgArqUp').show();" rendered="false"
                                               update="msgs" action="#{importarMB.LeParametro}">
                                    <p:graphicImage url="assets/img/icone_postosdeservico.png" width="120" />
                                    <f:actionListener binding="#{importarMB.setParametro(4)}"/>
                                </p:commandLink>
                                <p:commandLink oncomplete="PF('dlgArqUp').show();" rendered="false"
                                               update="msgs" action="#{importarMB.LeParametro}">
                                    <p:outputLabel value="#{localemsgs.PostoServicos}"
                                                   style="color:#022a48;"/>
                                    <f:actionListener binding="#{importarMB.setParametro(4)}"/>
                                </p:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" style="width: 100%; text-align: center;">
                                <p:commandLink oncomplete="PF('dlgArqUp').show();"
                                               update="msgs" action="#{importarMB.LeParametro}">
                                    <p:graphicImage url="assets/img/icone_filiais.png" width="120" />
                                    <f:actionListener binding="#{importarMB.setParametro(5)}"/>
                                </p:commandLink>
                                <p:commandLink oncomplete="PF('dlgArqUp').show();"
                                               update="msgs" action="#{importarMB.LeParametro}">
                                    <p:outputLabel value="#{localemsgs.Filiais}"
                                                   style="color:#022a48;"/>
                                    <f:actionListener binding="#{importarMB.setParametro(5)}"/>
                                </p:commandLink>
                            </h:panelGrid>
                        </p:panelGrid>
                    </p:panel>
                </p:dialog>
            </h:form>

            <h:form id="uploadForm">
                <p:dialog widgetVar="dlgArqUp" positionType="absolute" responsive="true"
                          draggable="false" modal="true" closable="true" resizable="false" dynamic="true"
                          showEffect="drop" hideEffect="drop" closeOnEscape="false" width="400" styleClass="dialogo"
                          style="border-top:4px solid #3C8DBC !important; max-width:95% !important;overflow:hidden !important;background-color:#EEE !important;">
                    <f:facet name="header">
                        <img src="#{importarMB.imagem}" height="40" width="40"/>
                        <p:spacer width="5px"/>
                        <h:outputText value="#{localemsgs.Arquivo}" style="color:#022a48" />
                    </f:facet>
                    <div  style="text-align: justify; width: 100%; padding-bottom: 20px">
                        <h:outputText value="#{localemsgs.DicaDownload}"/>
                    </div>
                    <div  style="text-align: center; width: 100%; padding-bottom: 20px">
                        <h:panelGrid columns="1" style="text-align: center; width: 100%;">
                            <p:commandLink ajax="false">
                                <p:graphicImage url="assets/img/icones_satmob_download_50x50.png"/>
                                <p:fileDownload value="#{importarMB.downloadFile}"/>
                            </p:commandLink>

                            <p:commandLink ajax="false">
                                <p:outputLabel value="#{localemsgs.Template}" />
                                <p:fileDownload value="#{importarMB.downloadFile}"/>
                            </p:commandLink>
                        </h:panelGrid>
                    </div>

                    <div  style="text-align: justify; width: 100%; padding-bottom: 10px">
                        <h:outputText value="#{localemsgs.ArrasteArquivo}:"/>
                    </div>

                    <div  style="text-align: center; width: 100%; height: 150px">
                        <p:fileUpload id="upload" fileUploadListener="#{importarMB.HandleFileUpload}"
                                      allowTypes="/(\.|\/)(xlsx)$/" label="#{localemsgs.Pesquisar}" fileLimit="1" auto="true"
                                      invalidFileMessage="#{localemsgs.ArquivoInvalido}"
                                      dragDropSupport="true" fileLimitMessage="#{localemsgs.QtdArquivosInvalida}"
                                      update="msgDeatalhada" previewWidth="10" skinSimple="true">
                            <h:outputText value="#{localemsgs.ArrasteAqui}" id="ArrasteAqui"
                                          style="text-align: justify; color: lightgray; top: 30px; position: relative;"/>
                        </p:fileUpload>
                    </div>
                </p:dialog>
            </h:form>

            <h:form id="formErrosImportacao" >
                <p:dialog widgetVar="dlgErrosImportacao" header="#{localemsgs.ErrosImportacao}" positionType="absolute"
                          modal="true" closable="true" resizable="false" dynamic="true" draggable="false" responsive="true"
                          showEffect="drop" hideEffect="drop" height="310" styleClass="dialogo"
                          style="border-top:4px solid #3C8DBC !important; max-width:95% !important;overflow:hidden !important;background-color:#EEE !important">
                    <p:panel style="background-color: transparent; height: 100%" styleClass="panelMenu">
                        <h:outputText value="#{importarMB.handler.sucesso}. #{importarMB.handler.erros.size()} #{localemsgs.Erros}:"/>
                        <p:dataTable value="#{importarMB.handler.erros}" var="erros" scrollable="true" scrollWidth="100%" scrollHeight="255"
                                     style="font-size: 12px; background: white">
                            <p:column headerText="#{localemsgs.Erros}">
                                <h:outputText value="#{erros}"/>
                            </p:column>
                        </p:dataTable>
                    </p:panel>
                </p:dialog>
            </h:form>

            <ui:insert name="loading" >
                <ui:include src="assets/template/loading.xhtml" />
            </ui:insert>

            <script type="text/javascript">
                // <![CDATA[
                function openNewTab() {
                    window.open("", "_blank");
                }

                function TirarFoto(permite, tipo) {
                    if (permite == 'S') {
                        $('[id*="txtDescricaoArq"]').val(tipo);
                        rc();
                        $('[id*="uploadFotosRelatorio_input"]').click();
                    }
                }

                $(document).ready(function () {
                    setTimeout(function () {
                        $('.sate-icon-grid').css('display', 'block');
                    }, 300);
                });

                function NaoPermitido(CodigoSecao) {
                    $.MsgBoxVermelhoOk('#{localemsgs.Aviso}', '#{localemsgs.AcessoNegado}<br><br>#{localemsgs.CodigoServico}: ' + CodigoSecao);
                }
                
                function ValidarAcesso(CodigoSecao, Link, Permite){
                    if(Permite =='S'){
                        if(Link.indexOf('dlg') == -1)
                            location.href = Link;
                        else
                            PF(Link).show();
                        return true;
                    }
                    else{
                        $.MsgBoxVermelhoOk('#{localemsgs.Aviso}', '#{localemsgs.AcessoNegado}<br><br>#{localemsgs.CodigoServico}: ' + CodigoSecao);
                        return false;
                    }
                }
                // ]]>
            </script>


            <footer>
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-3 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <div class="col-md-6 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important; text-align: center; margin-top: -1px !important;">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td rowspan="2">
                                        <img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="negrito">
                                        <h:outputText value="#{login.infoFilial.codFil}">
                                            <f:convertNumber pattern="0000"/>
                                        </h:outputText>
                                        #{login.infoFilial.descricao}
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-3 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
        </h:body>
    </f:view>
</html>