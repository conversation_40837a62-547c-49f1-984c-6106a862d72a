<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      style="overflow:hidden !important; max-height:100% !important;">
    <h:head>
        <link rel="icon" href="../assets/img/MonetaIcon.png" />
        <title>MSL</title>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
        <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet" />            
        <link type="text/css" href="../assets/css/menu.css" rel="stylesheet" />
        <link type="text/css" href="../assets/css/animate.css" rel="stylesheet" />
        <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
        <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript"></script>
        <style>
            i:not(.fa-circle-o):not(.fa-angle-left):not(.fa-angle-right){
                width:30px !important;
                text-align:center !important;
                font-size:12pt !important;
                margin-left:0px !important;
                padding-right: 16px !important; 
            }

            .treeview-menu li{
                white-space: pre-wrap !important;
                height: auto !important;
                padding-top:0px !important;
                padding-bottom:0px !important;
            }

            .treeview-menu a:not([target="ifrPage"]):not([href*="dashboard"]){
                opacity:0.3;
                display:none !important;
            }

            li, li a{
                text-transform: capitalize !important;
            }
        </style>
    </h:head>
    <h:body id="h">
        <aside class="main-sidebar">
            <section class="sidebar">
                <!-- Area de Pesquisa -->
                <div class="sidebar-form">
                    <div class="input-group">
                        <input type="text" name="q" class="form-control" placeholder="#{localemsgs.Pesquisar}..." />
                        <span class="input-group-btn">
                            <button type="submit" name="search" id="search-btn" class="btn btn-flat">
                                <i class="fa fa-search"></i>
                            </button>
                        </span>
                    </div>
                </div>

                <!-- Itens do Menu -->
                <ul class="sidebar-menu" data-widget="tree">
                    <!--<li class="active">
                        <a href="#">
                            <i class="fa fa-dashboard"></i><span>#{localemsgs.Dashboard}</span>
                        </a>
                    </li>-->

                    <li class="treeview">
                        <a href="#">
                            <i class="fa fa-handshake"></i><span>#{localemsgs.Comercial}</span>
                            <span class="pull-right-container">
                                <i class="fa fa-angle-left pull-right"></i>
                            </span>
                        </a>
                        <ul class="treeview-menu">
                           <li><a href="../comercial/clientes.xhtml?ref=msl" target="ifrPage"><i class="fa fa-circle-o"></i>#{localemsgs.Clientes}</a></li>
                           <li><a href="../comercial/contatos.xhtml?ref=msl" target="ifrPage"><i class="fa fa-circle-o"></i>#{localemsgs.Contatos}</a></li>
                           <li><a href="../comercial/produtos.xhtml?ref=msl" target="ifrPage"><i class="fa fa-circle-o"></i>#{localemsgs.Produtos}</a></li>
                           <li><a href="../comercial/contratos.xhtml?ref=msl" target="ifrPage"><i class="fa fa-circle-o"></i>#{localemsgs.Contratos}</a></li>
                           <li><a href="../comercial/proposta.xhtml?ref=msl" target="ifrPage"><i class="fa fa-circle-o"></i>#{localemsgs.Propostas}</a></li>
                        </ul>
                    </li>
                    <li class="treeview">
                        <a href="#">
                            <i class="fa fa-map-o"></i><span>#{localemsgs.Operacoes}</span>
                            <span class="pull-right-container">
                                <i class="fa fa-angle-left pull-right"></i>
                            </span>
                        </a>
                        <ul class="treeview-menu">
                           <li><a href="../operacoes/dashboard.xhtml"><i class="fa fa-circle-o"></i>#{localemsgs.Dashboard}</a></li>
                           <li><a href="../operacoes/escaladia.xhtml?ref=msl" target="ifrPage"><i class="fa fa-circle-o"></i>#{localemsgs.EscalaDia}</a></li>
                           <li><a href="../operacoes/supervisao.xhtml?ref=msl" target="ifrPage"><i class="fa fa-circle-o"></i>#{localemsgs.Supervisao}</a></li>
                           <li><a href="../operacoes/rotasupervisao.xhtml?ref=msl" target="ifrPage"><i class="fa fa-circle-o"></i>#{localemsgs.RotasSup}</a></li>
                           <li><a href="../operacoes/mapa_direccion.xhtml?ref=msl" target="ifrPage"><i class="fa fa-circle-o"></i>#{localemsgs.TransporteValores}</a></li>
                           <li><a href="../operacoes/pedidos.xhtml?ref=msl" target="ifrPage"><i class="fa fa-circle-o"></i>#{localemsgs.Pedidos}</a></li>
                           <li><a href="../operacoes/rotasvalores.xhtml?ref=msl" target="ifrPage"><i class="fa fa-circle-o"></i>#{localemsgs.Rotas}</a></li>
                           <!--<li><a href="../operacoes/coaf.xhtml?ref=msl" target="ifrPage"><i class="fa fa-circle-o"></i>#{localemsgs.CoafComunic}</a></li>-->
                           <li><a href="../operacoes/direccion_completo.xhtml?ref=msl" target="ifrPage"><i class="fa fa-circle-o"></i>#{localemsgs.TodasRotas}</a></li>
                        </ul>
                    </li>

                    
                    <li class="treeview">
                        <a href="#">
                            <i class="fa fa-edit"></i><span>#{localemsgs.Cadastros}</span>
                            <span class="pull-right-container">
                                <i class="fa fa-angle-left pull-right"></i>
                            </span>
                        </a>
                        <ul class="treeview-menu">
                           <li><a href="../cadastros/filiais.xhtml?ref=msl" target="ifrPage"><i class="fa fa-circle-o"></i>#{localemsgs.Filiais}</a></li>
                           <li><a href="../cadastros/veiculos.xhtml?ref=msl" target="ifrPage"><i class="fa fa-circle-o"></i>#{localemsgs.Veiculos}</a></li>
                        </ul>
                    </li>
                    
                    <li class="treeview">
                        <a href="#">
                            <i class="fa fa-users"></i><span>#{localemsgs.RecursosHumanos}</span>
                            <span class="pull-right-container">
                                <i class="fa fa-angle-left pull-right"></i>
                            </span>
                        </a>
                        <ul class="treeview-menu">
                           <li><a href="../recursoshumanos/funcion.xhtml?ref=msl" target="ifrPage"><i class="fa fa-circle-o"></i>#{localemsgs.Funcionarios}</a></li>
                           <li><a href="../recursoshumanos/pstserv.xhtml?ref=msl" target="ifrPage"><i class="fa fa-circle-o"></i>#{localemsgs.PostoServicos}</a></li>
                           <li><a href="../recursoshumanos/pessoas.xhtml?ref=msl" target="ifrPage"><i class="fa fa-circle-o"></i>#{localemsgs.Pessoas}</a></li>
                           <li><a href="../recursoshumanos/registrospontos.xhtml?ref=msl" target="ifrPage"><i class="fa fa-circle-o"></i>#{localemsgs.FolhaPonto}</a></li>
                           <li><a href="javascript:void(0)"><i class="fa fa-circle-o"></i>#{localemsgs.Check_In}</a></li>
                           <li><a href="javascript:void(0)"><i class="fa fa-circle-o"></i>#{localemsgs.DSEIndividual}</a></li>
                           <li><a href="javascript:void(0)"><i class="fa fa-circle-o"></i>#{localemsgs.FuncoesAdm}</a></li>
                        </ul>
                    </li>
                    <li class="treeview">
                        <a href="#">
                            <i class="fa fa-file-text-o"></i><span>#{localemsgs.Relatorios}</span>
                            <span class="pull-right-container">
                                <i class="fa fa-angle-left pull-right"></i>
                            </span>
                        </a>
                        <ul class="treeview-menu">
                           <li><a href="../relatorio/portal.xhtml?ref=msl" target="ifrPage"><i class="fa fa-circle-o"></i>#{localemsgs.SPMEW}</a></li>
                           <li><a href="../relatorio/inspecoes.xhtml?ref=msl" target="ifrPage"><i class="fa fa-circle-o"></i>#{localemsgs.Inspecoes}</a></li>
                           <li><a href="../relatorio/prefatura.xhtml?ref=msl" target="ifrPage"><i class="fa fa-circle-o"></i>#{localemsgs.PreFatura}</a></li>
                           <li><a href="../relatorio/prefaturaclifat.xhtml?ref=msl" target="ifrPage"><i class="fa fa-circle-o"></i>#{localemsgs.PreFaturaCliente}</a></li>                           
                        </ul>
                    </li>
                    
                    <li class="treeview">
                        <a href="#">
                            <i class="fas fa-door-closed"></i><span>#{localemsgs.CaixaForte}</span>
                            <span class="pull-right-container">
                                <i class="fa fa-angle-left pull-right"></i>
                            </span>
                        </a>
                        <ul class="treeview-menu">
                            <li><a href="../cxforte/custodia_pre.xhtml?ref=msl" target="ifrPage"><i class="fa fa-circle-o"></i>#{localemsgs.Custodia}</a></li>
                            <li><a href="../cxforte/entrada.xhtml?ref=msl" target="ifrPage"><i class="fa fa-circle-o"></i>#{localemsgs.EntradaCxForte}</a></li>
                        </ul>
                    </li>
                    <!--<li class="treeview">
                        <a href="#">
                            <i class="fa fa-money"></i><span>???Tesouraria???</span>
                            <span class="pull-right-container">
                                <i class="fa fa-angle-left pull-right"></i>
                            </span>
                        </a>
                        <ul class="treeview-menu">
                           <li><a href="../tesouraria/tes_entradas.xhtml?ref=msl" target="ifrPage"><i class="fa fa-circle-o"></i>??? TesourariaEnt ???</a></li>
                        </ul>
                    </li>-->
                    <li class="treeview">
                        <a href="#">
                            <i class="fa fa-lock"></i><span>#{localemsgs.CofreInteligente}</span>
                            <span class="pull-right-container">
                                <i class="fa fa-angle-left pull-right"></i>
                            </span>
                        </a>
                        <ul class="treeview-menu">
                           <li><a href="../cofre/dashboard_geral.xhtml"><i class="fa fa-circle-o"></i>#{localemsgs.Dashboard}</a></li>
                           <li><a href="../cofre/cofre.xhtml?ref=msl" target="ifrPage"><i class="fa fa-circle-o"></i>#{localemsgs.CofresGerenciados}</a></li>
                        </ul>
                        <a href="#">
                            <i class="fa fa-lock"></i><span>#{localemsgs.CofreInteligenteMov}</span>
                            <span class="pull-right-container">
                                <i class="fa fa-angle-left pull-right"></i>
                            </span>
                        </a>
                    </li>
                    <li class="treeview">
                        <a href="#">
                            <i class="fa fa-dollar"></i><span>#{localemsgs.Faturamento}</span>
                            <span class="pull-right-container">
                                <i class="fa fa-angle-left pull-right"></i>
                            </span>
                        </a>
                        <ul class="treeview-menu">
                           <li><a href="../faturamento/os_vig.xhtml?ref=msl" target="ifrPage"><i class="fa fa-circle-o"></i>#{localemsgs.OS}</a></li>
                           <li><a href="javascript:void(0)"><i class="fa fa-circle-o"></i>#{localemsgs.Guias}</a></li>
                           <li><a href="javascript:void(0)"><i class="fa fa-circle-o"></i>#{localemsgs.FechamentoRotas}</a></li>
                           <li><a href="javascript:void(0)"><i class="fa fa-circle-o"></i>#{localemsgs.FechamentoFaturamentoTransportes}</a></li>
                           <li><a href="javascript:void(0)"><i class="fa fa-circle-o"></i>#{localemsgs.FechamentoFaturamentoServicos}</a></li>
                           <li><a href="javascript:void(0)"><i class="fa fa-circle-o"></i>#{localemsgs.FaturarVendas}</a></li>
                           <li><a href="../faturamento/nfiscal.xhtml?ref=msl" target="ifrPage"><i class="fa fa-circle-o"></i>#{localemsgs.NotasFiscais}</a></li>
                        </ul>
                    </li>
                    <li class="treeview">
                        <a href="#">
                            <i class="fa fa-money"></i><span>#{localemsgs.Tesouraria}</span>
                            <span class="pull-right-container">
                                <i class="fa fa-angle-left pull-right"></i>
                            </span>
                        </a>
                        <ul class="treeview-menu">
                           <li><a href="../tesouraria/tes_entradas.xhtml?ref=msl" target="ifrPage"><i class="fa fa-circle-o"></i>#{localemsgs.Entrada}</a></li>
                           <li><a href="../tesouraria/tes_saidas.xhtml?ref=msl" target="ifrPage"><i class="fa fa-circle-o"></i>#{localemsgs.Saida}</a></li>
                        </ul>
                    </li>
                    
                    <li class="treeview">
                        <a href="#">
                            <i class="fa fa-get-pocket"></i><span>#{localemsgs.CaixaForte}</span>
                            <span class="pull-right-container">
                                <i class="fa fa-angle-left pull-right"></i>
                            </span>
                        </a>
                        <ul class="treeview-menu">
                           <li><a href="../cxforte/entrada.xhtml?ref=msl" target="ifrPage"><i class="fa fa-circle-o"></i>#{localemsgs.Entrada}</a></li>
                        </ul>
                    </li>
                    
                    <li class="treeview">
                        <a href="#">
                            <i class="fa fa-cog"></i><span>#{localemsgs.Configuracoes}</span>
                            <span class="pull-right-container">
                                <i class="fa fa-angle-left pull-right"></i>
                            </span>
                        </a>
                        <ul class="treeview-menu">
                           <li><a href="../configuracoes/acessos.xhtml?ref=msl" target="ifrPage"><i class="fa fa-circle-o"></i>#{localemsgs.Usuarios}</a></li>
                        </ul>
                    </li>
                    <li class="active" style="color: orange !important">
                        <a href="param.xhtml" style="color: orange !important">
                            <i class="fa fa-repeat"></i><span>#{localemsgs.TrocarFilial}</span>
                        </a>
                    </li>
                    <li style="color:red !important">
                        <a href="index.xhtml" style="color:red !important">
                            <i class="fa fa-sign-out"></i><span>#{localemsgs.Sair}</span>
                        </a>
                    </li>
                </ul>
            </section>
        </aside>
        <script>
            $(document).ready(function () {
                $('a[target="ifrPage"]').click(function () {
                    $('#ifrPage').css('display', 'none');
                    $('#imgLoad').css('display', '');
                });
                $('#ifrPage').on('load', function () {
                    CorrigirLayout();

                    setTimeout(function () {
                        $('#ifrPage').css('display', '');
                        $('#imgLoad').css('display', 'none');
                    }, 500);
                });

                setInterval(function () {
                    CorrigirLayout();
                }, 250);
            });

            function CorrigirLayout() {  
                $ifrPage = $('#ifrPage').contents();
                $ifrPage.find('.footer-body').css('box-shadow', 'none').css('color', '#000').css('background', '#F9FAFC').find('.container:eq(0)').css('padding-left', '20px').css('color', '#000');
                $ifrPage.find('.footer-body').find('.ui-clock').css('color', '#000');
                $ifrPage.find('#divDadosFilial, #divBotaoVoltar').css('display', 'none');
                $ifrPage.find('#divTopoTela').attr('class', 'col-md-7 col-sm-12 col-xs-12');
                $ifrPage.find('#divCalendario, [id$="calendario-topo"]').attr('class', 'col-md-5 col-sm-12 col-xs-12').css('text-align', 'right');
                $ifrPage.find('header').css('box-shadow', 'none').css('background', 'transparent').css('background-image', 'none').css('top', '7px').css('padding-left', '4px');
                $ifrPage.find('#h').css('background-image', 'none').css('background', 'transparent').css('background-color', 'transparent');
                $ifrPage.find('#TopoDash .Empresa').parent('div').css('display', 'none');
                $ifrPage.find('.ItemDash:nth-child(1)').parent('div').attr('class', 'col-md-12 col-sm-12 col-xs-12');
                $ifrPage.find('.footer-logos').find('tbody tr td:nth-child(1)').css('opacity', '0');
                //$ifrPage.find('#main').css('min-height', 'calc(100vh - 160px)').css('height', 'calc(100vh - 160px)').css('max-height', 'calc(100vh - 160px)').css('top', '109px');

                let styleGrid = '';
                /*styleGrid += ' @media only screen and (max-width: 3000px) and (min-width: 641px) {';
                styleGrid += '     thead tr th,';
                styleGrid += '     thead tr td{';
                styleGrid += '         background: linear-gradient(to bottom, #222d32, #222d32) !important;';
                styleGrid += '         border:thin solid #111c21 !important;';
                styleGrid += '     }';
                styleGrid += ' }';*/
                
                styleGrid += ' @media only screen and (max-width: 750px) and (min-width: 10px) {';
                styleGrid += '    #main{';
                styleGrid += '         min-height: calc(100vh - 190px) !important;';
                styleGrid += '         height: calc(100vh - 190px) !important;';
                styleGrid += '         max-height: calc(100vh - 190px) !important;';
                styleGrid += '         top: 64px !important;';
                styleGrid += '         position: relative !important;';
                styleGrid += '     }';
                styleGrid += ' }';

                $ifrPage.find('head style').prepend(styleGrid);
            }
        </script>
    </h:body>
</html>