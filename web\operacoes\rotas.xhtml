<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:o="http://omnifaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/img/favicon.png" />
            <title>
                #{localemsgs.SatMOB}
            </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <link type="text/css" href="../assets/css/flag-icon.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/style.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/stylePage.css" rel="stylesheet" />
            <link type="text/css" href="../assets/css/font-awesome.min.css" rel="stylesheet" />
            <script src="../assets/scripts/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />
            <style>
                .ui-button-icon-left{
                    position:absolute;
                    z-index: 999 !important;
                    width:40px !important;
                    top:18px !important;
                }

                .ui-button-icon-left::before {
                    font-size: 14pt !important;
                    margin-left:3px !important;
                }

                .FundoMapa{
                    height: 100% !important;
                    background-color: #FFF;
                    border: thin solid #CCC;
                    padding:10px 0px 10px 15px !important;
                    border-radius: 4px !important;
                    border-top:4px solid #3C8DBC !important;
                }

                #main{
                    background-color:#ECF0F5 !important;
                    padding:16px 14px 14px 14px !important;
                    overflow:hidden !important;
                    height: calc(100% - 0px) !important
                }

                footer{
                    min-height: 10px !important;
                    height: 10px !important;
                    max-height: 10px !important;
                    line-height:10px !important;
                    bottom:0px !important;
                    margin-bottom:0px !important;
                    padding:0px !important;
                    background-color: red !important;
                }

                #footer-toggle i{
                    padding-top:12px !important;
                }

                .TextoRodape{
                    height: 10px !important;
                    margin:0px !important;
                    padding:0px !important;
                }

                .Rotas{
                    border:thin solid #DDD !important;
                    max-height:calc(100vh - 100px) !important;
                    min-height:calc(100% - 0px) !important;
                    overflow:hidden !important;
                    padding:0px 3px 3px 3px !important;
                    width:100% !important;
                    border-radius:4px !important;
                    margin:0px !important;
                    background-color: #FFF !important;
                    /*box-shadow:2px 2px 3px #EEE;*/
                }

                .botao {
                    display: inline-block !important;
                    color: #fff !important;
                    width: 125px !important;
                    height: 40px !important;
                    padding: 0 20px !important;
                    background: #3479A3 !important;
                    border-radius: 5px !important;
                    outline: none !important;
                    border: none !important;
                    cursor: pointer !important;
                    text-align: center !important;
                    transition: all 0.2s linear !important;
                    letter-spacing: 0.05em !important;
                    margin-top:3px !important;
                }

                #legend{
                    overflow:auto !important;
                    min-height: calc(100vh - 200px) !important;
                    height: calc(100vh - 200px) !important;
                    max-height: calc(100vh - 200px) !important;
                }

                #legend div{
                    padding-top:6px !important;
                    padding-left:8px !important;
                    padding-right:8px !important;
                    margin-bottom:10px !important;
                }

                #firstHeading{
                    color: #3479A3;
                    margin: 0px!important;
                    line-height:25px !important;
                }

                .tableVeiculo {
                    border-collapse: collapse;
                }

                .tableVeiculo td, .tableVeiculo th {
                    border: 1px solid #ddd;
                    padding: 5px;
                }

                .tableVeiculo tr:nth-child(even){
                    background-color: #f2f2f2;
                }

                .tableVeiculo tr:hover {
                    background-color: #88BBD9;
                }

                .tableVeiculo th {
                    padding-bottom: 4px;
                }

                .gm-style-iw p{
                    line-height: 12px !important;
                }

                .gm-style-iw{
                    min-height: 410px !important;
                    min-width: 410px !important;
                    overflow:hidden !important;
                }

                .gm-style-iw div:first-child{
                    padding:0px 3px 0px 0px !important;
                    /*min-height: 410px !important;*/
                    overflow:hidden !important;
                }

                a:hover,
                a:focus {
                    color:#3479A3;
                }

                .botaoProgramacaoGeral {
                    position:absolute !important;
                    height: 40px !important;
                    cursor: pointer !important;
                    transition: all 0.2s linear !important;
                    z-index:10 !important;
                    bottom: 45px !important;
                    left: 15px !important;
                }

                #btMapaTotal{
                    position:absolute !important;
                    min-width: 150px !important;
                    width: 150px !important;
                    max-width: 150px !important;
                }

                #btMapaTotal:hover{
                    color:#FFF !important;
                }

                #legend div[id^="legenda"]{
                    width:100% !important;
                    padding-top:0px !important;
                    padding-bottom:0px !important;
                }

                div[ref="infoRota"]{
                    overflow-x:auto !important;
                }


                .fa-clock-o{
                    font-size: 14pt !important;
                    top:17px !important;
                    height:20px !important;
                }

                ::-webkit-scrollbar {
                    width: 7px;
                    height: 8px;
                    font-weight: 500 !important;
                }

                ::-webkit-scrollbar-track-piece {
                    background: #ccc;
                    border-radius: 20px;
                }

                ::-webkit-scrollbar-thumb:horizontal {
                    width: 10%;
                    background: #999;
                    border-radius: 20px;
                    border-radius: 5px;
                }

                ::-webkit-scrollbar-thumb:vertical {
                    height: 5px;
                    background: #999;
                    border-radius: 25px;
                }

                @media only screen and (max-width: 700px) and (min-width: 10px) {
                    #firstHeading{
                        font-size:12pt !important;
                        line-height:15px !important;
                    }

                    #divFooterTimer{
                        display:none !important;
                    }

                    #legend{
                        min-height:200px !important;
                    }

                    .FundoMapa{
                        overflow:auto !important;
                    }

                    #mapGoogle{
                        height: 430px !important;
                    }

                    .gm-style-iw-d,
                    .gm-style-iw-c{
                        overflow: hidden !important;
                        overflow-x: hidden !important;
                        padding-right: 0px !important;
                    }

                    .gm-style-iw{
                        padding-right: 0px !important;
                        min-height: 290px !important;
                        max-height: 290px !important;
                        min-width: 250px !important;
                        overflow-x: hidden !important;
                        overflow-y: hidden !important;
                        font-size:8pt !important;
                        padding-bottom:0px !important;
                        margin:0px !important;
                    }

                    .gm-style-iw div:first-child{
                        padding-right: 0px !important;
                        padding:0px 0px 0px 0px !important;
                        min-height: 270px !important;
                        max-height: 270px !important;
                        overflow-x: hidden !important;
                        overflow-y: auto !important;
                        font-size:8pt !important;
                        padding-bottom:0px !important;
                        margin:0px !important;
                        -webkit-overflow-scrolling: touch;
                    }

                    .gm-style-iw p{
                        max-width:400px;
                        white-space: nowrap;
                        text-align:left !important;
                        height: 10px !important;
                        line-height: 10px !important;
                        padding:0px !important;
                        margin:4px 0px 0px 0px !important;
                    }

                    div[ref="infoRota"]{
                        overflow:hidden !important;
                        overflow-x:auto !important;
                    }
                }


                .modal {
                    display: none; /* Hidden by default */
                    position: fixed; /* Stay in place */
                    z-index: 1; /* Sit on top */
                    padding-top: 100px; /* Location of the box */
                    left: 0;
                    top: 0;
                    width: 100%; /* Full width */
                    height: 100%; /* Full height */
                    overflow: auto; /* Enable scroll if needed */
                    background-color: #cdcdcd; /* Fallback color */
                    background-color: rgba(205,205,205,0.4); /* Black w/ opacity */
                }

                /* Modal Content */
                .modal-content {
                    background-color: #fefefe;
                    margin: auto;
                    padding: 20px;
                    border: 1px solid #888;
                    width: 80%;
                    color: black;
                }

                /* The Close Button */
                .close {
                    color: #aaaaaa;
                    float: right;
                    font-size: 28px;
                    font-weight: bold;
                }

                .close:hover,
                .close:focus {
                    color: #000;
                    text-decoration: none;
                    cursor: pointer;
                }

                .jconfirm-content-pane{
                    color: #666 !important;
                }

                .jconfirm-title{
                    color:#3498db !important;
                }

            </style>
        </h:head>                      
        <h:body id="h" style="overflow:hidden !important; max-height:100% !important">
            <f:metadata>
                <f:viewAction action="#{valores.Persistencia(login.pp, login.satellite)}"/>
                <f:viewAction action="#{valores.inserirPosicaoIncilRegra30Min()}"/>
                <f:viewAction action="#{valores.carregarMapa()}"/>
            </f:metadata>

            <p:growl id="msgs"/>

            <div id="body">
                <ui:include src="/botao_panico.xhtml"/>

                <header>
                    <h:form id="cabecalho">
                        <div class="ui-grid ui-grid-responsive">
                            <div class="ui-grid-row cabecalho">
                                <div id="divTopoTela" class="col-md-4 col-sm-12 col-xs-12">
                                    <img src="../assets/img/icones_satmob_carroforte.png" height="40" style="margin-top:-6px !important;" />
                                    <label class="TituloPagina">#{localemsgs.RotasValores}</label>
                                    <label class="TituloDataHora">
                                        <h:outputText value="#{localemsgs.Data}: "/>
                                        <span><h:outputText id="dataDia" value="#{valores.dataTela}" converter="conversorDia"/></span>
                                    </label>
                                </div>

                                <div id="divDadosFilial" class="col-md-3 col-sm-12 col-xs-6" style="text-align: center !important;">
                                    <label class="FilialNome">#{valores.filialDesc}<label id="btTrocarFilial">#{localemsgs.TrocarFilial}</label></label>
                                    <label class="FilialEndereco">#{valores.filiais.endereco}</label>
                                    <label class="FilialBairroCidade">#{valores.filiais.bairro}, #{valores.filiais.cidade}/#{valores.filiais.UF}</label>
                                </div>

                                <div id="divCalendario" class="col-md-4 col-sm-10 col-xs-6" style="text-align: right !important">
                                    <p:commandLink action="#{valores.RotaAnterior}" update="main cabecalho">
                                        <p:graphicImage url="../assets/img/botao_anterior.png"/>
                                    </p:commandLink>

                                    <p:calendar id="calendario" styleClass="calendario" showOn="button" navigator="true"
                                                pattern="#{mascaras.padraoData}" value="#{valores.dataTela}" maxdate="#{valores.ultimoDia}" 
                                                locale="#{localeController.getCurrentLocale()}" converter="conversorData">
                                        <p:ajax event="dateSelect" listener="#{valores.SelecionarData}" update="main cabecalho" />
                                    </p:calendar>

                                    <p:commandLink action="#{valores.RotaPosterior}" update="main cabecalho">
                                        <p:graphicImage url="../assets/img/botao_proximo.png"/>
                                    </p:commandLink>
                                </div>

                                <div id="divBotaoVoltar" class="col-md-1 col-sm-2 col-xs-2">
                                    <p:commandLink title="#{localemsgs.Voltar}"
                                                   onclick="window.history.back();" action="#">
                                        <p:graphicImage url="../assets/img/icone_voltar_branco.png" height="40"/>
                                    </p:commandLink>
                                </div>
                            </div>

                            <div class="ui-grid-row" style="display:none !important;">
                                <p:panel id="status" class="ui-grid-col-12 cabecalhoFilial">
                                    <div class="ui-grid-col-4">
                                        #{localemsgs.Filial}: #{valores.filialDesc}
                                    </div>
                                    <div class="ui-grid-col-4">
                                        #{localemsgs.QtdRotas}: #{valores.total}
                                    </div>
                                    <div class="ui-grid-col-4">

                                    </div>
                                </p:panel>
                            </div>
                        </div>
                    </h:form>
                </header>

                <h:form id="main" style="max-height: calc(100vh - 90px) !important">
                    <div class="FundoMapa" style="position: relative;">
                        <a href="mapa_total.xhtml?codfil=#{valores.codfil}&amp;pedidos=S&amp;datatela=#{valores.dataTela}" class="botaoProgramacaoGeral" title="#{localemsgs.Pedidos}" style="padding:8px 6px 0px 6px !important; width:90px !important; height:90px !important; border-radius:50%; background-color: #032a49; box-shadow: 2px 2px 3px #BBB; bottom: 150px !important; text-shadow:1px 1px #000; border:2px solid #002a49;">
                            <label style="width: 36px; height:36px;position:absolute; top:-10px; right: -9px; border-radius: 50%; background-color: #F00222; color:#FFF; font-weight: bold; text-align:center; border: 2px solid #F00; padding-top:5px;">#{valores.qtdePedidos}</label>
                            <i class="fa fa-clock-o" title="#{localemsgs.Pedidos}" style="color:#FFF; font-size:21pt !important; padding-top:9px !important; text-align:center; width:48px !important;height:48px !important; margin-left:12px"></i>
                            <label style="font-size:8pt !important; color:#FFF; display:block; white-space: pre-wrap !important; text-align:center; margin-top:-6px !important;">#{localemsgs.Pedidos}</label>
                        </a>

                        <a href="mapa_total.xhtml?codfil=#{valores.codfil}" class="botaoProgramacaoGeral" title="#{localemsgs.ProgramacaoGeral}" style="padding:9px 6px 0px 6px !important; width:90px !important; height:90px !important; border-radius:50%; background-color: forestgreen; box-shadow: 2px 2px 3px #BBB; border:2px solid darkgreen; text-shadow:1px 1px #000;">
                            <i class="fa fa-list" title="#{localemsgs.ProgramacaoGeral}" style="color:#FFF; font-size:16pt; padding-top:9px !important; text-align:center; width:40px !important;height:40px !important; margin-left:17px"></i>
                            <label style="font-size:8pt !important; color:#FFF; display:block; white-space: pre-wrap !important; text-align:center; margin-top:-5px !important">#{localemsgs.ProgramacaoGeral}</label>
                        </a>

                        <p:panel styleClass="painelCadastro" id="painelCadastro" style="padding:0px !important; height: 100% !important;">
                            <p:panelGrid columns="2" columnClasses="ui-grid-col-10,ui-grid-col-2"
                                         layout="grid" styleClass="ui-panelgrid-blank" style="padding:0px !important; height: 100% !important;">
                                <p:panel>
                                    <div id="mapGoogle" style="height: calc(100vh - 147px); padding:0px !important; margin: 0px !important"></div>
                                    <label style="font-size: 30pt; color:#CCC; width: 100% !important; text-align: center; height:180px; padding: 5px 0px 5px 0px; position: absolute !important; top:0; right:0; bottom:0;left:0; margin:auto; z-index: 999 !important;display:#{valores.centro ne null and valores.centro ne ''? 'none': ''}">
                                        <i class="fa fa-map-marker" style="display:block; font-size:60pt !important"></i>
                                        #{localemsgs.NaoHaDados}
                                    </label>
                                </p:panel>
                                <p:panel class="Rotas" style="display:#{valores.centro ne null and valores.centro ne ''? '': 'none'}">
                                    <p:commandButton id="btAtualizarMapa" value="#{localemsgs.AtualizarMapa}" actionListener="#{valores.RecarregaGeraPontos}" icon="fa fa-refresh"
                                                     styleClass="botao" style="width: 100% !important; height: 40px !important; padding: 0px !important; text-align:center !important; margin-top:0px; margin-bottom:6px;"
                                                     type="submit" update="main cabecalho" />
                                    <div id="legend"><h3 style="color:#FFF !important; background-color: #3479A3 !important; border-radius:3px 3px 0px 0px; margin-top: 3px !important; margin-bottom:0px !important; padding-bottom:5px !important; padding-top:3px !important;padding-left:7px; font-size:14pt !important; font-weight:500 !important;"><i class="fa fa-map" style="font-size:12pt !important;"></i>&nbsp;#{localemsgs.Rotas}<label style="background-color:#FFF; color:steelblue; font-weight: bold; font-size:10pt; text-align:center; width: 60px; border-radius: 40px; padding-top:3px !important; float:right !important; padding-bottom:2px !important; margin-right: 6px; margin-top:1px">#{valores.qtdeRotasMapa}</label></h3></div>
                                </p:panel>
                            </p:panelGrid>
                            <script type="text/javascript">
                                // <![CDATA[
                                #{valores.modaisPrd}

                                AjustarTamanho();

                                function initMap() {
                                    var map = new google.maps.Map(document.getElementById('mapGoogle'), {
                                        zoom: 10,
                                        center: #{valores.centro},
                                        gestureHandling: 'cooperative'
                                    });
                                    var legend = document.getElementById('legend');

                                #{valores.markers}
                                #{valores.infoWindows}
                                #{valores.contentStrings}
                                }

                                $(document)
                                        .on('click', 'a[href*="operacoes/mapa_direccion.xhtml"]', function () {
                                            $(this).find('i').attr('class', 'fa fa-refresh fa-spin fa-fw');
                                        })
                                        .on('click', '[id*="btMapaTotal"]', function () {
                                            $(this).find('.fa-clock-o').addClass('fa-spin').addClass('fa-fw');
                                        })
                                        .on('click', '#btTrocarFilial', function () {
                                            top.location.href = '../param.xhtml';
                                        })
                                        .on('click', '.botaoProgramacaoGeral', function () {
                                            $(this).find('i').attr('class', 'fa fa-refresh fa-spin fa-fw');
                                        })
                                        ;

                                function AjustarTamanho() {
                                    $('#legend').css('height', ($('.Rotas').height() - 0) + 'px');
                                    if ($(document).width() <= 700)
                                        $('.FundoMapa').css('max-height', ($('body').height() - 202) + 'px');
                                    else
                                        $('.FundoMapa').css('max-height', '100%');
                                }

                                function AbreEfetividade($obj) {
                                    let HTML = $obj;

                                    $.ModalDialogCallBack('large',
                                            'fa fa-line-chart fa-lg',
                                            '#{localemsgs.ResumoEfetividadeRota}',
                                            HTML.toString(),
                                            'blue',
                                            'fa fa-times',
                                            '#{localemsgs.Fechar}',
                                            '',
                                            '',
                                            '',
                                            '#{localeController.number}');
                                }

                                $(window).resize(function () {
                                    $('#legend').css('height', ($('.Rotas').height() - 0) + 'px');

                                    if ($(document).width() <= 700) {
                                        $('#legend').css('min-height', '200px');
                                        $('.FundoMapa').css('max-height', ($('body').height() - 202) + 'px');
                                    } else {
                                        $('#legend').css('min-height', '200px');
                                        $('.FundoMapa').css('max-height', '100%');
                                    }
                                });
                                // ]]>                                                                                          
                            </script>
                            <script src="https://maps.googleapis.com/maps/api/js?key=#{login.googleApiOper}&amp;callback=initMap"></script>

                        </p:panel>
                    </div>
                </h:form>
            </div>
            <ui:insert name="loading" id="loadJava">
                <ui:include src="../assets/template/loading.xhtml" />
            </ui:insert>                                         
            <footer>
                <!--<div class="footer-toggler">
                    <a href="#footer-toggle" id="footer-toggle" >
                        <i class="fa fa-bars" style="font-size: 18px"></i>
                    </a>
                </div>-->
                <div class="footer-body" id="footer-body" style="padding:0px !important;min-height:10px !important; max-height:40px !important;">
                    <div class="container" style="min-height:10px !important;max-height:40px !important;">
                        <div id="divFooterTimer" class="col-md-3 col-sm-3 col-xs-3" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-time" style="min-height:10px !important">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" class="TextoRodape" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" class="TextoRodape" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6 col-sm-6 col-xs-7" style="padding:0px !important; min-height:10px !important;max-height:40px !important;">
                            <table class="footer-user" style="min-height:10px !important">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-3 col-sm-3 col-xs-5" style="padding:0px !important; min-height:10px !important;max-height:40px !important;height:40px !important;">
                            <table class="footer-logos" style="min-height:10px !important;max-height:40px !important;height:40px !important;">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}"
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>

            <ui:include src="/assets/popups/senha_randomica.xhtml"/>
        </h:body>

    </f:view>

</html>
