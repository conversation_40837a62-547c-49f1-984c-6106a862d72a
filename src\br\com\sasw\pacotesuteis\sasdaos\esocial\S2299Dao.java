/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos.esocial;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ESocial.S2299;
import SasBeans.ESocial.S2299.DescFolha;
import SasBeans.ESocial.S2299.DetOper;
import SasBeans.ESocial.S2299.DetPlano;
import SasBeans.ESocial.S2299.DetVerbas;
import SasBeans.ESocial.S2299.InfoPerApur;
import SasDaos.FPrescDescEmprestDao;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class S2299Dao {

    public List<S2299> get(String codFil, String compet, String ambiente, Persistencia persistencia) throws Exception {
        try {
            

            List<S2299> retorno = new ArrayList<>();
            int indice, subIndice;
            String sql = " Select (select max(sucesso) from  ( "
                    + "                         (Select case when isnull(count(*),0) > 0 then 1 else -1 end sucesso "
                    + "                                From XmleSocial z "
                    + "                             where z.Identificador = Funcion.Matr "
                    + "                             and z.evento = 'S-2299' "
                    + "                             and z.CodFil = ? "
                    + "                             and z.Compet = ? "
                    + "                             and z.Ambiente = ? "
                    + "                             and (z.Xml_Retorno like '%aguardando%' or z.Xml_Retorno = '')) "
                    + "                     union "
                    + "                         (Select case when isnull(count(*),0) > 0 then 0 else -1 end sucesso "
                    + "                             From XmleSocial z  "
                    + "                             where z.Identificador = Funcion.Matr "
                    + "                             and z.evento = 'S-2299' "
                    + "                             and z.CodFil = ? "
                    + "                             and z.Compet = ? "
                    + "                             and z.Ambiente = ? "
                    + "                             and z.Xml_Retorno like '%<ocorrencia>%') "
                    + "                     union "
                    + "                         (Select case when isnull(count(*),0) > 0 then 2 else -1 end sucesso "
                    + "                             From XmleSocial z  "
                    + "                             where z.Identificador = Funcion.Matr "
                    + "                             and z.evento = 'S-2299' "
                    + "                             and z.CodFil = ? "
                    + "                             and z.Compet = ? "
                    + "                             and z.Ambiente = ? "
                    + "                             and z.Xml_Retorno like '%<processamento><cdResposta>201</cdResposta>%')) a) sucesso , "
                    + " Case when Filiais.TipoPessoa = 'J' then '1' else '2' end ideEmpregador_tpInsc, "
                    + " Filiais.CNPJ ideEmpregador_nrInsc, "
                    + " Convert(Bigint,FPRescisoes.Matr) Matr, "
                    + "substring(replace(convert(varchar,FPRescisoes.DtAvisoIni,111),'/','-'),0,11) detAvPrevio_dtAvPrv,\n"
                    + "substring(replace(convert(varchar,FPRescisoes.DtAvisoFim,111),'/','-'),0,11) detAvPrevio_dtPrevDeslig,\n"                    
                    + " FPRescisoes.CodMovFP, "
                    + " Funcion.CPF ideVinculo_cpfTrab, "
                    + " Funcion.PIS ideVinculo_nisTrab, "
                    + " Case when Len(Funcion.CodPonto) > 0 then Funcion.CodPonto else convert(varchar,convert(BigInt,Funcion.Matr)) end ideVinculo_matricula, "
                    + " Case when FPRescisoes.Iniciativa = 2  "
                    + "       and FPRescisoes.Motivo = 32 "
                    + " 	then '01' " //Justa Causa, Iniciatiava Empregador. 
                    + " 	when FPRescisoes.Iniciativa = 2  "
                    + "       and FPRescisoes.Motivo = 31 "
                    + " 	then '02' " //Sem Justa Causa, Iniciatiava Empregador. 
                    + " 	when FPRescisoes.Iniciativa = 2  "
                    + "       and FPRescisoes.Motivo = 46 "
                    + " 	then '03' " //Antecipação contrato, Iniciatiava Empregador. 
                    + " 	when FPRescisoes.Iniciativa = 1 "
                    + "       and FPRescisoes.Motivo = 46 "
                    + " 	then '04' " //Antecipação contrato, Iniciatiava Empregado. 
                    + " 	when FPRescisoes.Motivo = 45 "
                    + " 	then '06' " //Falecimento empregado. 
                    + " 	when FPRescisoes.Motivo = 60 "
                    + " 	then '10' " //Falecimento empregado. 
                    + " 	when FPRescisoes.Motivo = 80 "
                    + " 	then '11' " //Transferencia mesmo grupo. 
                    + " 	when FPRescisoes.Iniciativa = 1 "
                    + " 	then '07' " //Rescisão Iniciatiava Empregado. 
                    + " 	when FPRescisoes.Iniciativa = 3 "
                    + " 	then '05' " //Rescisão reciproca. 
                    + " 	end infoDeslig_mtvDeslig, "
                    + " FPRescisoes.DtDemissao infoDeslig_dtDeslig, "
                    + " Case when FPRescisoes.Tipo = 2 then 'S' else 'N' end infoDeslig_indPagtoAPI, "
                    + " FPRescisoes.DtAvisoFim infoDeslig_dtProjFimAPI, "
                    + " (Select  "
                    + "  Case when COUNT(*) > 0 then '2' else '0' end  "
                    + "  from FPLancamentos  "
                    + "  Inner join Verbas  on FPLancamentos.Verba = Verbas.Verba "
                    + "  Inner join FPFormulas  on Verbas.Formula = FPFormulas.Formula "
                    + "  Where FPLancamentos.Matr = FPRescisoes.Matr "
                    + "    and FPLancamentos.CodMovFP = FPRescisoes.CodMovFP "
                    + "    and FPLancamentos.TipoFP = 'RES' "
                    + "    and FPFormulas.descricao like '%PENSAO ALIMENTICIA%') infoDeslig_pensAlim, "
                    + " (Select  "
                    + "  Isnull(Sum(ValorCalc),0) "
                    + "  from FPLancamentos  "
                    + "  Inner join Verbas  on FPLancamentos.Verba = Verbas.Verba "
                    + "  Inner join FPFormulas  on Verbas.Formula = FPFormulas.Formula "
                    + "  Where FPLancamentos.Matr = FPRescisoes.Matr "
                    + "    and FPLancamentos.CodMovFP = FPRescisoes.CodMovFP "
                    + "    and FPLancamentos.TipoFP = 'RES' "
                    + "    and FPFormulas.descricao like '%PENSAO ALIMENTICIA%') infoDeslig_vrAlim, "
                    + " Case when FPMensal.Proventos > 0 then Round(((Select  "
                    + "  Isnull(Sum(ValorCalc),0) "
                    + "  from FPLancamentos  "
                    + "  Inner join Verbas  on FPLancamentos.Verba = Verbas.Verba "
                    + "  Inner join FPFormulas  on Verbas.Formula = FPFormulas.Formula "
                    + "  Where FPLancamentos.Matr = FPRescisoes.Matr "
                    + "    and FPLancamentos.CodMovFP = FPRescisoes.CodMovFP "
                    + "    and FPLancamentos.TipoFP = 'RES' "
                    + "    and FPFormulas.descricao like '%PENSAO ALIMENTICIA%')/FPMensal.Proventos)*100,2) else 0 end infoDeslig_percAliment, "
                    + " (Select  "
                    + "  Case when COUNT(*) > 0 and FPRescisoes.Tipo = 2 then '2' " //Cumprimento Parcial Aviso. 
                    + "       when COUNT(*) = 0 and FPRescisoes.Tipo = 2 then '0' " //Cumprimento Total Aviso. 
                    + "  else '4' end " //Aviso indenizado ou não exigivel. 
                    + "  from FPLancamentos  "
                    + "  Inner join Verbas  on FPLancamentos.Verba = Verbas.Verba "
                    + "  Inner join FPFormulas  on Verbas.Formula = FPFormulas.Formula "
                    + "  Where FPLancamentos.Matr = FPRescisoes.Matr "
                    + "    and FPLancamentos.CodMovFP = FPRescisoes.CodMovFP "
                    + "    and FPLancamentos.TipoFP = 'RES' "
                    + "    and FPFormulas.descricao like '%AVISO PREVIO INDENIZADO%') infoDeslig_indCumprParc, "
                    + " Case when Funcion.TrabIntermitente = 'S' then ( "
                    + " Select Count(Distinct CtrOperv.Data) Qtde from CtrOperv  "
                    + " where CtrOperv.FuncSubs = FPRescisoes.Matr "
                    + "  and CtrOperv.Data between FPPeriodos.DtInicioP and  FPPeriodos.DtFinalP "
                    + "  and CtrOperv.  Flag_Excl <> '*' "
                    + " ) else 0 end infoDeslig_qtdDiasInterm, "
                    + " Funcion.TrabIntermitente, "
                    + " Case when Filiais.TipoPessoa = 'J' then '1' else '2' end ideEstabLot_tpInsc, "
                    + " Filiais.CNPJ ideEstabLot_nrInsc, "
                    + " (Filiais.CodFil*10000)+Filiais.CodFil ideEstabLot_codLotacao, "
                    + " 'RES'+Convert(varchar,FPRescisoes.CodMovFP) dmDev_ideDmDev, "
                    + " Substring(Funcion.OBS,1,35) infoDeslig_nrCertObito, "
                    //+ "  nbvf, "
                    + " (Select Isnull(Max(z.CNPJ),0) "
                    + "  from Funcion x"
                    + "  Left join Filiais z  on z.CodFil = x.CodFil "
                    + "  Where x.CPF = Funcion.CPF "
                    + "    and x.Situacao = 'A') sucessaoVinc_cnpjSucessora,  "
                    + " FPRescisoes.OBS sucessaoVinc_cnpjSucessora_BD_Segregados, Upper(Filiais.RazaoSocial) RazaoSocial "
                    + " from FPRescisoes "
                    + " Left join FPMensal  on FPMensal.CodMovFP = FPRescisoes.CodMovFP "
                    + "                    and FPMensal.TipoFP = 'RES' "
                    + "                    and FPMensal.Matr = FPRescisoes.Matr "
                    + " Left  join Filiais  on Filiais.CodFil = FPRescisoes.CodFil "
                    + " Inner Join Funcion  on FPRescisoes.Matr = Funcion.Matr "
                    + " Left  join FPPeriodos  on FPPeriodos.CodMovFP = FPRescisoes.CodMovFP "
                    + " Where FPRescisoes.CodFil = ? "
                    + "   and FPRescisoes.CodMovFP = ? "
                    + "   and Funcion.Vinculo not in ('D','E','S','A') ";
            //+ "   and FPRescisoes.Matr not in (Select Matr From FPRescisoes z where z.Matr = FPMensal.Matr and z.CodMovFP in (Select top 03 FPPeriodos.CodMovFP from FPPeriodos where FPPEriodos.CodMovFP < FPMensal.CodMovFP order by FPPeriodos.CodMovFP desc)) "; //Inclusão tratamento rescisões complementares.
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet);
            consulta.setString(ambiente);
            consulta.setString(codFil);
            consulta.setString(compet.substring(2, 4) + compet.substring(5));
            consulta.select();
            S2299 s2299;
            while (consulta.Proximo()) {
                s2299 = new S2299();
                s2299.setIdeEvento_indRetif("1");
                s2299.setIdeEvento_procEmi("1");
                s2299.setIdeEvento_verProc("Satellite eSocial");
                s2299.setSucesso(consulta.getInt("sucesso"));
                s2299.setMatr(consulta.getString("matr"));
                s2299.setIdeEmpregador_tpInsc(consulta.getString("ideEmpregador_tpInsc"));
                s2299.setIdeEmpregador_nrInsc(consulta.getString("ideEmpregador_nrInsc"));
                s2299.setIdeVinculo_cpfTrab(consulta.getString("ideVinculo_cpfTrab"));
                s2299.setIdeVinculo_nisTrab(consulta.getString("ideVinculo_nisTrab"));
                s2299.setIdeVinculo_trabIntermitente(consulta.getString("TrabIntermitente"));
                //if (persistencia.getEmpresa().equals("SATTRANSPORTER")) {
                if ((persistencia.getEmpresa().equals("SATBRASIFORT")) || (persistencia.getEmpresa().equals("SATTRANSPORTER")) ||
                       (consulta.getString("IdeVinculo_matricula") != "")) {
                    s2299.setIdeVinculo_matricula(consulta.getString("IdeVinculo_matricula"));
                } else {
                    s2299.setIdeVinculo_matricula(consulta.getString("Matr"));
                }
                //s2299.setIdeVinculo_matricula(consulta.getString("ideVinculo_matricula"));
                s2299.setdetAvPrevio_dtAvPrevio(consulta.getString("detAvPrevio_dtAvPrv"));
                s2299.setInfoDeslig_mtvDeslig(consulta.getString("infoDeslig_mtvDeslig"));
                s2299.setInfoDeslig_dtDeslig(consulta.getString("infoDeslig_dtDeslig"));
                s2299.setInfoDeslig_indPagtoAPI(consulta.getString("infoDeslig_indPagtoAPI"));
                s2299.setInfoDeslig_dtProjFimAPI(consulta.getString("infoDeslig_dtProjFimAPI"));
                s2299.setInfoDeslig_pensAlim(consulta.getString("infoDeslig_pensAlim"));
                s2299.setInfoDeslig_percAliment(consulta.getString("infoDeslig_percAliment"));
                s2299.setInfoDeslig_vrAlim(consulta.getString("infoDeslig_vrAlim"));
                s2299.setInfoDeslig_indCumprParc(consulta.getString("infoDeslig_indCumprParc"));
                s2299.setInfoDeslig_qtdDiasInterm(consulta.getString("infoDeslig_qtdDiasInterm"));
                s2299.setIdeEstabLot_tpInsc(consulta.getString("ideEstabLot_tpInsc"));
                s2299.setIdeEstabLot_nrInsc(consulta.getString("ideEstabLot_nrInsc"));
                s2299.setIdeEstabLot_codLotacao(consulta.getString("ideEstabLot_codLotacao"));
                s2299.setDmDev_ideDmDev(consulta.getString("dmDev_ideDmDev"));
                s2299.setInfoDeslig_nrCertObito(consulta.getString("infoDeslig_nrCertObito"));
                s2299.setIdeEvento_compet(consulta.getString("CodMovFP"));
                if (consulta.getString("sucessaoVinc_cnpjSucessora").length() > 1) {
                    s2299.setSucessaoVinc_cnpjSucessora(consulta.getString("sucessaoVinc_cnpjSucessora"));
                } else {
                    s2299.setSucessaoVinc_cnpjSucessora(consulta.getString("sucessaoVinc_cnpjSucessora_BD_Segregados"));
                }

                s2299.setInfoPerApur_ideEstabLot(new InfoPerApur());
                s2299.getInfoPerApur_ideEstabLot().setIdeEstabLot_detVerbas(new ArrayList<>());
                s2299.getInfoPerApur_ideEstabLot().setInfoSaudeColet_detOper(new ArrayList<>());
                
                // Preencher a tabela FPrescDescEmprest
                FPrescDescEmprestDao dFPRescDescEmprest = new FPrescDescEmprestDao();
                dFPRescDescEmprest.preencherComFuncionVerbaConsig(codFil, compet, 
                        s2299.getMatr(), persistencia);

                retorno.add(s2299);
            }

            sql = "Select convert(varchar,convert(BigInt,Funcion.Matr)) IdeVinculo_matricula, "
                    + " Case when Len(Funcion.CodPonto) > 0 then Funcion.CodPonto else convert(varchar,convert(BigInt,Funcion.Matr)) end IdeVinculo_matriculaCodPonto, "
                    + " Funcion.Matr MatrNaoCodPonto, "
                    + " Fornec.CNPJ detOper_cnpjOper, Fornec.OBS detOper_regANS, Round(Sum(ValorCalc),2) detOper_vrPgTit "
                    + " from FpLancamentos "
                    + " Left join Funcion  on Funcion.Matr = FPLancamentos.Matr"
                    + " inner join Verbas  on FpLancamentos.Verba = Verbas.Verba "
                    + " inner join Fornec  on Verbas.CodForn = Fornec.Codigo "
                    + " Where CodMovFP = ? "
                    + "       and TipoFP = 'RES' "
                    + "       and Verbas.PlanoSaude = 'S' "
                    + "       and FPLancamentos.Flag_Excl <> '*' "
                    + "       and FPLancamentos.ValorCalc > 0 "
                    + " Group by Fornec.CNPJ, Fornec.OBS, FpLancamentos.Matr, Funcion.CodPonto, Funcion.Matr ";
            consulta = new Consulta(sql, persistencia);
            consulta.setString(compet.substring(2, 4) + compet.substring(5));
            consulta.select();
            S2299.DetOper dtOper;
            while (consulta.Proximo()) {
                s2299 = new S2299();
                dtOper = new DetOper();
                dtOper.setDetOper_cnpjOper(consulta.getString("detOper_cnpjOper"));
                dtOper.setDetOper_regANS(consulta.getString("detOper_regANS"));
                dtOper.setDetOper_vrPgTit(consulta.getString("detOper_vrPgTit"));
                dtOper.setDetOper_detPlano(new ArrayList<>());
                //if (persistencia.getEmpresa().equals("SATTRANSPORTER")) {
                if ((persistencia.getEmpresa().equals("SATBRASIFORT")) || (persistencia.getEmpresa().equals("SATTRANSPORTER")) || 
                       (consulta.getString("IdeVinculo_matriculaCodPonto") != "")) {
                    s2299.setIdeVinculo_matricula(consulta.getString("IdeVinculo_matriculaCodPonto"));                    
                } else {
                    s2299.setIdeVinculo_matricula(consulta.getString("IdeVinculo_matricula"));
                }
                s2299.setMatrFloat(consulta.getString("MatrNaoCodPonto"));

                indice = retorno.indexOf(s2299);
                if (indice >= 0) {
                    retorno.get(indice).getInfoPerApur_ideEstabLot().getInfoSaudeColet_detOper().add(dtOper);
                }
            }

            sql = "Select convert(varchar,convert(BigInt,Funcion.Matr)) IdeVinculo_matricula, "
                    + " Case when Len(Funcion.CodPonto) > 0 then Funcion.CodPonto else convert(varchar,convert(BigInt,Funcion.Matr)) end IdeVinculo_matriculaCodPonto,\n"
                    + " Case when F5_Dep.Tipo = 'C' then '01' "
                    +//Conjugue\n +
                    "     when F5_Dep.Tipo = 'F' then '03' "
                    +//Filho\n +
                    "	when F5_Dep.Tipo = 'M' then '09' "
                    +//Mae\n +
                    "	when F5_Dep.Tipo = 'P' then '09' "
                    +//Pai\n +
                    "	when F5_Dep.Tipo = 'E' then '03' "
                    +//Enteado\n +
                    "	when F5_Dep.Tipo = 'O' then '99' "
                    +//Agregados/Outros\n +
                    "	end detPlano_tpDep,\n"
                    + "F5_Dep.CPF detPlano_cpfDep,\n"
                    + "F5_Dep.Nome detPlano_nmDep,\n"
                    + "F5_Dep.Dt_Nasc detPlano_dtNascto,\n"
                    + "F5_Dep.ValorPS+F5_Dep.ValorPO detPlano_vlrPgDep,\n"
                    + "Fornec.OBS detOper_regANS\n"
                    + "from F5_Dep\n "
                    + "Left join Funcion  on Funcion.Matr = F5_Dep.Matr \n"
                    + "inner join Verbas  on F5_Dep.VerbaPS = Verbas.Verba\n"
                    + "inner join Fornec  on Verbas.CodForn = Fornec.Codigo\n"
                    + "Where F5_Dep.Tipo <> 'T'\n"
                    + "  and (F5_Dep.DepPS = 'S')\n"
                    + "union\n"
                    + "Select convert(varchar,convert(BigInt,Funcion.Matr)) IdeVinculo_matricula, "
                    + " Case when Len(Funcion.CodPonto) > 0 then Funcion.CodPonto else convert(varchar,convert(BigInt,Funcion.Matr)) end IdeVinculo_matriculaCodPonto,\n"
                    + " Case when F5_Dep.Tipo = 'C' then '01' "
                    +//Conjugue\n +
                    "     when F5_Dep.Tipo = 'F' then '03' "
                    +//Filho\n +
                    "	when F5_Dep.Tipo = 'M' then '09' "
                    +//Mae\n +
                    "	when F5_Dep.Tipo = 'P' then '09' "
                    +//Pai\n +
                    "	when F5_Dep.Tipo = 'E' then '03' "
                    +//Enteado\n +
                    "	when F5_Dep.Tipo = 'O' then '99' "
                    +//Agregados/Outros\n +
                    "	end detPlano_tpDep,\n"
                    + "F5_Dep.CPF detPlano_cpfDep,\n"
                    + "F5_Dep.Nome detPlano_nmDep,\n"
                    + "F5_Dep.Dt_Nasc detPlano_dtNascto,\n"
                    + "F5_Dep.ValorPS+F5_Dep.ValorPO detPlano_vlrPgDep,\n"
                    + "Fornec.OBS detOper_regANS\n"
                    + "from F5_Dep\n"
                    + "Left join Funcion  on Funcion.Matr = F5_Dep.Matr \n"
                    + "inner join Verbas  on F5_Dep.VerbaPO = Verbas.Verba\n"
                    + "inner join Fornec  on Verbas.CodForn = Fornec.Codigo\n"
                    + "Where F5_Dep.Tipo <> 'T'\n"
                    + "  and (F5_Dep.DepPO = 'S')";
            consulta = new Consulta(sql, persistencia);
            consulta.select();
            S2299.DetPlano detPlano;
            while (consulta.Proximo()) {
                s2299 = new S2299();
                detPlano = new DetPlano();
                dtOper = new DetOper();
                detPlano.setDetPlano_tpDep(consulta.getString("detPlano_tpDep"));
                detPlano.setDetPlano_cpfDep(consulta.getString("detPlano_cpfDep"));
                detPlano.setDetPlano_nmDep(consulta.getString("detPlano_nmDep"));
                detPlano.setDetPlano_dtNascto(consulta.getString("detPlano_dtNascto"));
                detPlano.setDetPlano_vlrPgDep(consulta.getString("detPlano_vlrPgDep"));
                //if (persistencia.getEmpresa().equals("SATTRANSPORTER")) {
                if ((persistencia.getEmpresa().equals("SATBRASIFORT")) || (persistencia.getEmpresa().equals("SATTRANSPORTER")) ||
                        (consulta.getString("IdeVinculo_matriculaCodPonto") != "")) {
                    s2299.setIdeVinculo_matricula(consulta.getString("IdeVinculo_matriculaCodPonto"));
                } else {
                    s2299.setIdeVinculo_matricula(consulta.getString("IdeVinculo_matricula"));
                }

                indice = retorno.indexOf(s2299);
                if (indice >= 0) {
                    dtOper.setDetOper_regANS(consulta.getString("detOper_regANS"));
                    subIndice = retorno.get(indice).getInfoPerApur_ideEstabLot().getInfoSaudeColet_detOper().indexOf(dtOper);
                    if (subIndice > 0) {
                        retorno.get(indice).getInfoPerApur_ideEstabLot().getInfoSaudeColet_detOper().get(subIndice).getDetOper_detPlano().add(detPlano);
                    }
                }
            }

            sql = "Select convert(varchar,convert(BigInt,Funcion.Matr)) IdeVinculo_matricula, "
                    + "Case when Len(Funcion.CodPonto) > 0 then Funcion.CodPonto else convert(varchar,convert(BigInt,Funcion.Matr)) end IdeVinculo_matriculaCodPonto, FPLancamentos.Verba detVerbas_codRubr, FPLancamentos.Verba detVerbas_ideTabRubr, "
                    + " Case when FPLancamentos.Valor = 0 then 1 else FPLancamentos.Valor end detVerbas_qtdRubr, "
                    + " Round(FPLancamentos.ValorCalc/Case when FPLancamentos.Valor = 0 then 1 else FPLancamentos.Valor end,2) detVerbas_vrUnit, "
                    + " Round(FPLancamentos.ValorCalc,2) detVerbas_vrRubr "
                    + " from FpLancamentos "
                    + " Left join Funcion  on Funcion.Matr = FPLancamentos.Matr "
                    + " Left join Verbas  on Verbas.Verba = FPLancamentos.Verba"
                    + " Where CodMovFP = ? "
                    + "   and TipoFP = 'RES' "
                    + "   and FpLancamentos.ValorCalc > 0 "
                    + "   and FPLancamentos.Flag_Excl <> '*' "
                    + " and Verbas.Formula not in('0221','0222','0223','0225','0226','0003','0013','0023','0033')";
            consulta = new Consulta(sql, persistencia);
            consulta.setString(compet.substring(2, 4) + compet.substring(5));
            consulta.select();
            S2299.DetVerbas detVerbas;
            while (consulta.Proximo()) {
                s2299 = new S2299();
                detVerbas = new DetVerbas();
                detVerbas.setDetVerbas_codRubr(consulta.getString("detVerbas_codRubr"));
                detVerbas.setDetVerbas_ideTabRubr(consulta.getString("detVerbas_ideTabRubr"));
                detVerbas.setDetVerbas_qtdRubr(consulta.getString("detVerbas_qtdRubr"));
                detVerbas.setDetVerbas_vrUnit(consulta.getString("detVerbas_vrUnit"));
                detVerbas.setDetVerbas_vrRubr(consulta.getString("detVerbas_vrRubr"));
                //if (persistencia.getEmpresa().equals("SATTRANSPORTER")) {
                if ((persistencia.getEmpresa().equals("SATBRASIFORT")) || (persistencia.getEmpresa().equals("SATTRANSPORTER")) ||
                        (consulta.getString("IdeVinculo_matriculaCodPonto") != "")) {
                    s2299.setIdeVinculo_matricula(consulta.getString("IdeVinculo_matriculaCodPonto"));
                } else {
                    s2299.setIdeVinculo_matricula(consulta.getString("IdeVinculo_matricula"));
                }              
               
                indice = retorno.indexOf(s2299);
                if (indice >= 0) {
                    retorno.get(indice).getInfoPerApur_ideEstabLot().getIdeEstabLot_detVerbas().add(detVerbas);
                    sql = "Select  "
                            + "    fpRescDescEmprest.Verba, " 
                            + "    fpRescDescEmprest.Banco, " 
                            + "    fpRescDescEmprest.Contrato, " 
                            + "    fpRescDescEmprest.Obs "
                            + " from  fpRescDescEmprest  " 
                            + "   left  join Verbas "
                            + "       on Verbas.Verba = fpRescDescEmprest.Verba  "
                            + "Where fpRescDescEmprest.CodMovFP = ? "
                            + "  and fpRescDescEmprest.CodFil = ? "
                            + "  and fpRescDescEmprest.Matr = ? " 
                            + "  and fpRescDescEmprest.Verba = ? "
                            + "  and Verbas.NatEsocial = '9253'";
                    Consulta consultaDesconto = new Consulta(sql, persistencia);
                    consultaDesconto.setString(compet.substring(2, 4) + compet.substring(5));
                    consultaDesconto.setString(codFil);
                    consultaDesconto.setString(retorno.get(indice).getMatr());
                    consultaDesconto.setString(detVerbas.getDetVerbas_codRubr());
                    consultaDesconto.select();

                    if (consultaDesconto.Proximo()) {
                        DescFolha descFolha = new S2299.DescFolha();
                        descFolha.setDescFolha_instFinanc(consultaDesconto.
                                getString("banco"));
                        descFolha.setDescFolha_nrDoc(consultaDesconto.
                                getString("contrato"));
                        descFolha.setDescFolha_observacao(consultaDesconto.
                                getString("obs"));
                        detVerbas.setDescFolha(descFolha);
                    }

                }

            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("S2299Dao.get - " + e.getMessage() + "\r\n"
                    + "");
        }
    }
}
